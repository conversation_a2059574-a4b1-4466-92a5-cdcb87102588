// 临时调试脚本 - 检查 store 方法导出
import { useSubscriptionStore } from './src/stores/subscription.ts'

console.log('检查 subscription store 方法...')

const store = useSubscriptionStore()

console.log('Store 对象:', store)
console.log('可用方法:', Object.keys(store))

// 检查特定方法
const methods = [
  'fetchRedeemCodes',
  'createRedeemCode', 
  'updateRedeemCode',
  'deleteRedeemCode',
  'createCustomPackageRedeemCode'
]

methods.forEach(method => {
  console.log(`${method}:`, typeof store[method])
})
