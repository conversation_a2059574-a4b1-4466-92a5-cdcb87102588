<template>
  <div class="layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside class="sidebar" :width="sidebarWidth">
        <div class="logo-container">
          <img src="/Logo_TunshuEdu_W.svg" alt="TunshuEdu Logo" class="sidebar-logo" />
          <h2 class="logo-title" v-if="!isCollapse">TunshuEdu BMS</h2>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isCollapse"
          :collapse-transition="false"
          background-color="#ffffff"
          text-color="#374151"
          active-text-color="#3b82f6"
        >
          <!-- 核心功能 -->
          <el-menu-item index="dashboard" @click="handleNavigation('/dashboard')">
            <el-icon><DataBoard /></el-icon>
            <span>总览</span>
          </el-menu-item>
          
          <!-- 数据管理 -->
          <el-menu-item index="subscription" @click="handleNavigation('/subscription')">
            <el-icon><ShoppingCart /></el-icon>
            <span>订阅管理</span>
          </el-menu-item>
          
          <el-menu-item index="/user-management" @click="handleNavigation('/user-management')">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          
          <el-menu-item index="analytics" disabled>
            <el-icon><TrendCharts /></el-icon>
            <span>数据分析</span>
          </el-menu-item>
          
          <!-- 系统功能 -->
          <el-menu-item index="reports" disabled>
            <el-icon><Document /></el-icon>
            <span>报表中心</span>
          </el-menu-item>
          
          <el-menu-item index="monitoring" disabled>
            <el-icon><Monitor /></el-icon>
            <span>系统监控</span>
          </el-menu-item>
          
          <el-menu-item index="settings" disabled>
            <el-icon><Setting /></el-icon>
            <span>系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              link
              class="collapse-btn"
              @click="toggleSidebar"
            >
              <el-icon size="20">
                <component :is="isCollapse ? 'Expand' : 'Fold'" />
              </el-icon>
            </el-button>
            
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item>
                <el-icon><HomeFilled /></el-icon>
                首页
              </el-breadcrumb-item>
              <el-breadcrumb-item v-if="$route.meta.title">
                {{ $route.meta.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-button
              link
              class="refresh-btn"
              @click="handleRefresh"
              :loading="dashboardStore.loading"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
            
            <el-dropdown @command="handleCommand">
              <div class="user-dropdown">
                <el-avatar
                  :src="authStore.user?.avatar_url"
                  :size="32"
                  class="user-avatar"
                >
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ authStore.user?.username }}</span>
                <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
              </div>
              
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="logout" divided>
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  DataBoard, 
  HomeFilled, 
  Expand, 
  Fold, 
  Refresh, 
  User, 
  ArrowDown, 
  SwitchButton,
  TrendCharts,
  Document,
  Monitor,
  Setting,
  ShoppingCart
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// 侧边栏状态
const isCollapse = ref(false)
const sidebarWidth = computed(() => isCollapse.value ? '64px' : '240px')

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  if (path === '/dashboard') return 'dashboard'
  if (path === '/subscription') return 'subscription'
  return 'dashboard'
})

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 刷新数据
const handleRefresh = async () => {
  try {
    await dashboardStore.refreshAll()
    ElMessage({
      type: 'success',
      message: '数据刷新成功'
    })
  } catch (error) {
    console.error('数据刷新失败:', error)
    ElMessage({
      type: 'error', 
      message: '数据刷新失败'
    })
  }
}

// 安全的路由导航
const handleNavigation = async (path: string) => {
  try {
    await router.push(path)
  } catch (error) {
    console.error('Navigation error:', error)
    ElMessage({
      type: 'error',
      message: '页面跳转失败'
    })
  }
}

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage({
        type: 'info',
        message: '个人资料功能开发中...'
      })
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '退出确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        authStore.logout()
        router.push('/login')
        ElMessage({
          type: 'success',
          message: '已成功退出登录'
        })
      } catch {
        // 用户取消操作
      }
      break
  }
}

// 初始化用户信息
onMounted(async () => {
  if (!authStore.user) {
    await authStore.init()
  }
})
</script>

<style scoped>
.layout {
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100%;
}

/* 侧边栏样式 - 简洁白色设计 */
.sidebar {
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  transition: width var(--duration-normal) var(--ease-out);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
  padding: 0 16px;
}

.sidebar-logo {
  height: 36px;
  width: auto;
  flex-shrink: 0;
}

.logo-title {
  color: #1e293b;
  font-size: 16px;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.02em;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
  background: transparent;
}

.sidebar-menu .el-menu-item {
  height: 56px;
  line-height: 56px;
  border-bottom: 1px solid #f1f5f9;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: #374151;
  font-weight: 500;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #f8fafc !important;
  color: #3b82f6 !important;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #eff6ff !important;
  color: #3b82f6 !important;
}

.sidebar-menu .el-menu-item .el-icon {
  margin-right: 12px;
  transition: color 0.3s ease;
}

.sidebar-menu .el-menu-item:hover .el-icon,
.sidebar-menu .el-menu-item.is-active .el-icon {
  color: #3b82f6;
}

.sidebar-menu .el-menu-item.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  color: #9ca3af;
}

.sidebar-menu .el-menu-item.is-disabled:hover {
  background: transparent !important;
  color: #9ca3af !important;
}

/* 主容器样式 */
.main-container {
  display: flex;
  flex-direction: column;
}

/* 顶部导航样式 - 简洁白色 */
.header {
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-5);
}

.collapse-btn {
  padding: 8px;
  color: #64748b;
  transition: color 0.3s ease;
}

.collapse-btn:hover {
  color: #3b82f6;
}

.breadcrumb {
  font-size: 14px;
  color: #6b7280;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-btn {
  padding: 8px;
  color: #6b7280;
  transition: color 0.3s ease;
}

.refresh-btn:hover {
  color: #3b82f6;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.user-dropdown:hover {
  background-color: #f8fafc;
}

.user-avatar {
  border: 2px solid #e2e8f0;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #334155;
}

.dropdown-arrow {
  color: #94a3b8;
  font-size: 12px;
}

/* 内容区域样式 - 简洁白色 */
.main-content {
  background: #f8fafc;
  padding: 24px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .breadcrumb {
    display: none;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .username {
    display: none;
  }
}
</style>