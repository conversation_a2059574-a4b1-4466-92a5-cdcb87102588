<template>
  <div class="user-management-view">
    <!-- 页面标题 -->
    <div class="page-header mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">用户管理</h1>
      <p class="text-gray-600">管理系统用户、查看用户信息和活动统计</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-card-section">
      <UserStatsCard :stats="userManagementStore.stats" :loading="userManagementStore.loading" />
    </div>

    <!-- 用户表格 -->
    <div class="user-table-section">
      <UserTable
        :users="userManagementStore.users"
        :loading="userManagementStore.loading"
        :pagination="userManagementStore.pagination"
        :filters="userManagementStore.filters"
        @search="handleSearch"
        @filter-change="handleFilterChange"
        @page-change="handlePageChange"
        @sort-change="handleSortChange"
        @refresh="handleRefresh"
        @reset-filters="handleResetFilters"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserManagementStore } from '@/stores/user-management'
import UserStatsCard from '@/components/user-management/UserStatsCard.vue'
import UserTable from '@/components/user-management/UserTable.vue'
import type { UserFilters } from '@/types'

const userManagementStore = useUserManagementStore()

// 处理搜索
const handleSearch = async (query: string) => {
  try {
    await userManagementStore.searchUsers(query)
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  }
}

// 处理筛选变化
const handleFilterChange = async (filters: Partial<UserFilters>) => {
  try {
    await userManagementStore.applyFilters(filters)
  } catch (error) {
    console.error('筛选失败:', error)
    ElMessage.error('筛选失败')
  }
}

// 处理页面变化
const handlePageChange = async (page: number) => {
  try {
    await userManagementStore.changePage(page)
  } catch (error) {
    console.error('切换页面失败:', error)
    ElMessage.error('切换页面失败')
  }
}

// 页面大小固定为25，不需要处理页面大小变化

// 处理排序变化
const handleSortChange = async (prop: string, order: string) => {
  try {
    await userManagementStore.applyFilters({
      sort_by: prop,
      sort_order: order as 'asc' | 'desc'
    })
  } catch (error) {
    console.error('排序失败:', error)
    ElMessage.error('排序失败')
  }
}

// 处理刷新
const handleRefresh = async () => {
  try {
    await userManagementStore.refresh()
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  }
}

// 处理重置筛选
const handleResetFilters = async () => {
  try {
    await userManagementStore.resetFilters()
    ElMessage.success('筛选条件已重置')
  } catch (error) {
    console.error('重置筛选失败:', error)
    ElMessage.error('重置筛选失败')
  }
}

// 页面初始化
onMounted(async () => {
  try {
    await userManagementStore.refresh()
  } catch (error) {
    console.error('初始化失败:', error)
    // 检查是否是认证错误
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as any
      if (axiosError.response?.status === 401) {
        ElMessage.error('请先登录管理员账户')
        return
      }
    }
    ElMessage.error('页面初始化失败，请检查网络连接或联系管理员')
  }
})
</script>

<style scoped>
.user-management-view {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
}

.stats-card-section {
  margin-bottom: 64px;
}

.user-table-section {
  margin-bottom: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management-view {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .stats-card-section {
    margin-bottom: 48px;
  }
}
</style> 