<template>
  <div class="subscription-view">
    <!-- 页面标题 -->
    <div class="page-header mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">订阅管理</h1>
      <p class="text-gray-600">管理用户订阅、支付订单、兑换码和积分账户</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-card-section">
      <SubscriptionStatsCard :stats="subscriptionStore.stats" :loading="subscriptionStore.loading" />
    </div>

    <!-- 标签页 -->
    <div class="subscription-tabs-container">
      <el-tabs v-model="activeTab" class="subscription-tabs" @tab-change="handleTabChange">
      <!-- 支付订单 -->
      <el-tab-pane label="支付订单" name="orders">
        <template #label>
          <div class="flex items-center gap-2">
            <el-icon><ShoppingCart /></el-icon>
            <span>支付订单</span>
            <el-badge :value="orderStats.pending" :hidden="!orderStats.pending" class="ml-1" />
          </div>
        </template>
        
        <DataTable
          ref="orderTableRef"
          :loading="subscriptionStore.loading"
          :data="orders"
          :pagination="orderPagination"
          :search-placeholder="paymentOrderConfig.searchPlaceholder"
          :filters="paymentOrderConfig.filters"
          :show-date-filter="paymentOrderConfig.showDateFilter"
          @search="handleOrderSearch"
          @refresh="handleOrderRefresh"
        >
          <template #columns="{ formatCurrency, formatNumber, formatDateTime }">
            <el-table-column prop="id" label="ID" width="80" sortable="custom" />
            
            <el-table-column prop="order_no" label="订单号" width="180">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleViewOrder(row)">
                  {{ row.order_no }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column label="用户信息" min-width="200">
              <template #default="{ row }">
                <div class="user-info">
                  <div class="font-medium text-gray-900">{{ getUserDisplayNameForOrder(row) }}</div>
                  <div class="text-sm text-gray-500">{{ getDisplayEmail(row.user_email) }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="amount" label="金额" width="120" align="right">
              <template #default="{ row }">
                <span class="font-medium text-green-600">¥{{ formatCurrency(row.amount) }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="credits" label="积分" width="100" align="right">
              <template #default="{ row }">
                <span class="font-medium text-blue-600">{{ formatNumber(row.credits) }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="payment_method" label="支付方式" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusConfig('paymentMethod', row.payment_method).type" size="small">
                  {{ getStatusConfig('paymentMethod', row.payment_method).text }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusConfig('orderStatus', row.status).type" size="small">
                  {{ getStatusConfig('orderStatus', row.status).text }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="payment_time" label="支付时间" width="160" align="center">
              <template #default="{ row }">
                <div v-if="row.payment_time" class="text-sm">
                  {{ formatDateTime(row.payment_time) }}
                </div>
                <span v-else class="text-gray-400">未支付</span>
              </template>
            </el-table-column>

            <el-table-column prop="created_at" label="创建时间" width="160" align="center">
              <template #default="{ row }">
                <div class="text-sm">{{ formatDateTime(row.created_at) }}</div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-dropdown @command="(command: string) => handleOrderAction(command, row)">
                  <el-button link size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="view">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item command="edit" v-if="row.status === 'pending'">
                        <el-icon><Edit /></el-icon>
                        编辑订单
                      </el-dropdown-item>
                      <el-dropdown-item command="refund" v-if="row.status === 'paid'" divided>
                        <el-icon class="text-red-500"><RefreshLeft /></el-icon>
                        申请退款
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </template>
        </DataTable>
      </el-tab-pane>

      <!-- 兑换码管理 -->
      <el-tab-pane label="兑换码管理" name="redeem-codes">
        <template #label>
          <div class="flex items-center gap-2">
            <el-icon><Ticket /></el-icon>
            <span>兑换码管理</span>
          </div>
        </template>
        
        <div class="redeem-codes-section">
          <!-- 兑换码表格 -->
          <DataTable
            :loading="subscriptionStore.loading"
            :data="redeemCodes"
            :pagination="redeemCodePagination"
            :search-placeholder="redeemCodeConfig.searchPlaceholder"
            :filters="redeemCodeConfig.filters"
            :show-date-filter="redeemCodeConfig.showDateFilter"
                      @search="handleRedeemCodeSearch"
          @refresh="handleRedeemCodeRefresh"
        >
          <template #actions>
            <el-button type="primary" @click="handleCreateRedeemCode">
              <el-icon><Plus /></el-icon>
              创建兑换码
            </el-button>
          </template>
            <template #columns="{ formatNumber, formatDateTime }">
              <el-table-column prop="id" label="ID" width="80" sortable="custom" />
              
              <el-table-column prop="code" label="兑换码" width="150">
                <template #default="{ row }">
                  <el-button type="primary" link @click="handleViewRedeemCode(row)">
                    {{ row.code }}
                  </el-button>
                </template>
              </el-table-column>

              <el-table-column label="类型与价值" min-width="150">
                <template #default="{ row }">
                  <div v-if="row.credits" class="flex items-center gap-2">
                    <el-tag type="primary" size="small">积分兑换码</el-tag>
                    <span class="font-medium text-blue-600">{{ formatNumber(row.credits) }} 积分</span>
                  </div>
                  <div v-else-if="row.package_id" class="flex items-center gap-2">
                    <el-tag type="success" size="small">套餐兑换码</el-tag>
                    <span class="text-gray-600">{{ row.package_id }}</span>
                  </div>
                  <div v-else>
                    <el-tag type="info" size="small">通用兑换码</el-tag>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="description" label="描述" min-width="200">
                <template #default="{ row }">
                  <span class="text-gray-600">{{ row.description || '-' }}</span>
                </template>
              </el-table-column>

              <el-table-column label="使用情况" width="120" align="center">
                <template #default="{ row }">
                  <div class="text-sm">
                    <div class="font-medium">{{ row.used_count }}/{{ row.max_uses }}</div>
                    <div class="text-gray-500">{{ row.usage_rate.toFixed(1) }}%</div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="is_active" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.is_active ? 'success' : 'info'" size="small">
                    {{ row.is_active ? '激活' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="expires_at" label="过期时间" width="160" align="center">
                <template #default="{ row }">
                  <div v-if="row.expires_at" class="text-sm">
                    <div>{{ formatDateTime(row.expires_at) }}</div>
                    <el-tag v-if="row.is_expired" type="danger" size="small">已过期</el-tag>
                  </div>
                  <span v-else class="text-gray-400">永不过期</span>
                </template>
              </el-table-column>

              <el-table-column prop="created_at" label="创建时间" width="160" align="center">
                <template #default="{ row }">
                  <div class="text-sm">{{ formatDateTime(row.created_at) }}</div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template #default="{ row }">
                  <el-dropdown @command="(command: string) => handleRedeemCodeAction(command, row)">
                    <el-button link size="small">
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="view">
                          <el-icon><View /></el-icon>
                          查看详情
                        </el-dropdown-item>
                        <el-dropdown-item command="edit">
                          <el-icon><Edit /></el-icon>
                          编辑兑换码
                        </el-dropdown-item>
                        <el-dropdown-item command="toggle" :divided="true">
                          <el-icon><Switch /></el-icon>
                          {{ row.is_active ? '禁用' : '激活' }}
                        </el-dropdown-item>
                        <el-dropdown-item command="delete" class="text-red-500">
                          <el-icon class="text-red-500"><Delete /></el-icon>
                          删除兑换码
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </el-table-column>
            </template>
          </DataTable>
        </div>
      </el-tab-pane>

      <!-- 兑换记录 -->
      <el-tab-pane label="兑换记录" name="redemptions">
        <template #label>
          <div class="flex items-center gap-2">
            <el-icon><Star /></el-icon>
            <span>兑换记录</span>
          </div>
        </template>
        
        <DataTable
          :loading="subscriptionStore.loading"
          :data="redemptions"
          :pagination="redemptionPagination"
          :search-placeholder="redemptionConfig.searchPlaceholder"
          :filters="redemptionConfig.filters"
          :show-date-filter="redemptionConfig.showDateFilter"
          @search="handleRedemptionSearch"
          @refresh="handleRedemptionRefresh"
        >
          <template #columns="{ formatNumber, formatDateTime }">
            <el-table-column prop="id" label="ID" width="80" sortable="custom" />
            
            <el-table-column prop="redeem_code" label="兑换码" width="150">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleViewRedemption(row)">
                  {{ row.redeem_code }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column label="用户信息" min-width="200">
              <template #default="{ row }">
                <div class="user-info">
                  <div class="font-medium text-gray-900">{{ getUserDisplayNameForRedemption(row) }}</div>
                  <div class="text-sm text-gray-500">{{ getDisplayEmail(row.user_email) }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="兑换内容" min-width="150">
              <template #default="{ row }">
                <div v-if="row.credits_received" class="flex items-center gap-2">
                  <el-tag type="primary" size="small">积分</el-tag>
                  <span class="font-medium text-blue-600">{{ formatNumber(row.credits_received) }} 积分</span>
                </div>
                <div v-else-if="row.package_order_id" class="flex items-center gap-2">
                  <el-tag type="success" size="small">套餐</el-tag>
                  <span class="text-gray-600">订单 #{{ row.package_order_id }}</span>
                </div>
                <div v-else>
                  <el-tag type="info" size="small">其他</el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="redeemed_at" label="兑换时间" width="160" align="center">
              <template #default="{ row }">
                <div class="text-sm">{{ formatDateTime(row.redeemed_at) }}</div>
              </template>
            </el-table-column>

            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag type="success" size="small">兑换成功</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-dropdown @command="(command: string) => handleRedemptionAction(command, row)">
                  <el-button link size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="view">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item command="export">
                        <el-icon><Download /></el-icon>
                        导出记录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </template>
        </DataTable>
      </el-tab-pane>

      <!-- 积分账户 -->
      <el-tab-pane label="积分账户" name="credit-accounts">
        <template #label>
          <div class="flex items-center gap-2">
            <el-icon><CreditCard /></el-icon>
            <span>积分账户</span>
          </div>
        </template>
        
        <!-- TODO: 实现 CreditAccountTable 组件 -->
        <div class="text-center py-12 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <el-icon class="text-4xl mb-4"><CreditCard /></el-icon>
          <div class="text-lg font-medium mb-2">积分账户功能</div>
          <div class="text-sm">该功能正在开发中，敬请期待...</div>
        </div>
        <!--
        <CreditAccountTable
          :accounts="creditAccounts"
          :loading="subscriptionStore.loading"
          :pagination="creditAccountPagination"
          @page-change="handleCreditAccountPageChange"
        />
        -->
      </el-tab-pane>

      <!-- 积分交易 -->
      <el-tab-pane label="积分交易" name="credit-transactions">
        <template #label>
          <div class="flex items-center gap-2">
            <el-icon><Histogram /></el-icon>
            <span>积分交易</span>
          </div>
        </template>
        
        <!-- TODO: 实现 CreditTransactionTable 组件 -->
        <div class="text-center py-12 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <el-icon class="text-4xl mb-4"><Histogram /></el-icon>
          <div class="text-lg font-medium mb-2">积分交易功能</div>
          <div class="text-sm">该功能正在开发中，敬请期待...</div>
        </div>
        <!--
        <CreditTransactionTable
          :transactions="creditTransactions"
          :loading="subscriptionStore.loading"
          :pagination="creditTransactionPagination"
          @page-change="handleCreditTransactionPageChange"
        />
        -->
      </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 创建兑换码弹窗 -->
    <CreateRedeemCodeDialog
      v-model="showCreateRedeemCode"
      @created="handleRedeemCodeCreated"
    />

    <!-- 编辑兑换码弹窗 -->
    <EditRedeemCodeDialog
      v-model="showEditRedeemCode"
      :redeem-code="selectedRedeemCode"
      @updated="handleRedeemCodeUpdated"
    />
  </div>
</template>

<style scoped>
.user-info {
  line-height: 1.4;
}
</style>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ShoppingCart, MoreFilled, View, Edit, RefreshLeft, 
  Ticket, Search, Plus, Star, CreditCard, Histogram,
  Clock, Money, Check, TrendCharts, Bottom, Switch, Delete, Download
} from '@element-plus/icons-vue'
import { useSubscriptionStore } from '@/stores/subscription'
import type { 
  PaymentOrder, RedeemCode, RedeemCodeRedemption, 
  UserCreditAccount, CreditTransaction 
} from '@/types'

// 组件导入
import SubscriptionStatsCard from '@/components/subscription/SubscriptionStatsCard.vue'
import CreateRedeemCodeDialog from '@/components/subscription/CreateRedeemCodeDialog.vue'
import EditRedeemCodeDialog from '@/components/subscription/EditRedeemCodeDialog.vue'
import DataTable from '@/components/subscription/DataTable.vue'
import { getUserDisplayName, getDisplayEmail } from '@/utils/avatar'
import { 
  paymentOrderConfig, 
  redeemCodeConfig,
  redemptionConfig,
  getStatusConfig 
} from '@/components/subscription/SubscriptionConfig'

const subscriptionStore = useSubscriptionStore()

// 状态管理
const activeTab = ref('orders')
const orderTableRef = ref()
const showCreateRedeemCode = ref(false)
const showEditRedeemCode = ref(false)
const selectedRedeemCode = ref<RedeemCode | null>(null)

// 数据状态
const orders = ref<PaymentOrder[]>([])
const redeemCodes = ref<RedeemCode[]>([])
const redemptions = ref<RedeemCodeRedemption[]>([])
const creditAccounts = ref<UserCreditAccount[]>([])
const creditTransactions = ref<CreditTransaction[]>([])

// 搜索和筛选
// redeemCodeSearch 和 redeemCodeFilters 现在由 DataTable 内部处理

// 分页状态
const orderPagination = reactive({
  page: 1,
  page_size: 25,
  total: 0,
  pages: 0
})

const redeemCodePagination = reactive({
  page: 1,
  page_size: 25,
  total: 0,
  pages: 0
})

const redemptionPagination = reactive({
  page: 1,
  page_size: 25,
  total: 0,
  pages: 0
})

const creditAccountPagination = reactive({
  page: 1,
  page_size: 25,
  total: 0,
  pages: 0
})

const creditTransactionPagination = reactive({
  page: 1,
  page_size: 25,
  total: 0,
  pages: 0
})

// 计算订单统计
const orderStats = computed(() => {
  const stats = subscriptionStore.stats
  return {
    pending: stats?.pending_orders || 0,
    paid: stats?.paid_orders || 0
  }
})

// 标签页切换处理
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  loadTabData(tabName)
}

// 加载标签页数据
const loadTabData = async (tabName: string) => {
  try {
    switch (tabName) {
      case 'orders':
        await loadOrders()
        break
      case 'redeem-codes':
        await loadRedeemCodes()
        break
      case 'redemptions':
        await loadRedemptions()
        break
      case 'credit-accounts':
        await loadCreditAccounts()
        break
      case 'credit-transactions':
        await loadCreditTransactions()
        break
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage({
      type: 'error',
      message: '加载数据失败'
    })
  }
}

// 支付订单相关方法
const loadOrders = async () => {
  const params = {
    page: orderPagination.page,
    page_size: orderPagination.page_size
  }
  const response = await subscriptionStore.fetchPaymentOrders(params)
  orders.value = response.items
  orderPagination.total = response.total
  orderPagination.pages = response.pages
}

const handleOrderSearch = async (params: any) => {
  const response = await subscriptionStore.fetchPaymentOrders(params)
  orders.value = response.items
  orderPagination.total = response.total
  orderPagination.pages = response.pages
  orderPagination.page = params.page || 1
  orderPagination.page_size = params.page_size || 25
}

const handleOrderRefresh = () => {
  loadOrders()
}

// 查看订单详情
const handleViewOrder = (order: PaymentOrder) => {
  ElMessage({
    type: 'info',
    message: `查看订单详情: ${order.order_no}`
  })
}

// 处理订单操作
const handleOrderAction = (command: string, order: PaymentOrder) => {
  switch (command) {
    case 'view':
      handleViewOrder(order)
      break
    case 'edit':
      ElMessage({
        type: 'info',
        message: `编辑订单: ${order.order_no}`
      })
      break
    case 'refund':
      ElMessage({
        type: 'warning',
        message: `申请退款: ${order.order_no}`
      })
      break
  }
}

// 将PaymentOrder转换为getUserDisplayName需要的格式
const getUserDisplayNameForOrder = (order: PaymentOrder): string => {
  const userInfo = {
    username: order.user_username || '',
    email: order.user_email || '',
    wechat_nickname: order.user_wechat_nickname || null,
    nickname: order.user_nickname || null
  } as any
  
  return getUserDisplayName(userInfo)
}



// 兑换码相关方法
const loadRedeemCodes = async () => {
  const params = {
    page: redeemCodePagination.page,
    page_size: redeemCodePagination.page_size
  }
  const response = await subscriptionStore.fetchRedeemCodes(params)
  redeemCodes.value = response.items
  redeemCodePagination.total = response.total
  redeemCodePagination.pages = response.pages
}

const handleRedeemCodeSearch = async (params: any) => {
  const response = await subscriptionStore.fetchRedeemCodes(params)
  redeemCodes.value = response.items
  redeemCodePagination.total = response.total
  redeemCodePagination.pages = response.pages
  redeemCodePagination.page = params.page || 1
  redeemCodePagination.page_size = params.page_size || 25
}

const handleRedeemCodeRefresh = () => {
  loadRedeemCodes()
}

const handleViewRedeemCode = (code: RedeemCode) => {
  ElMessage({
    type: 'info',
    message: `查看兑换码详情: ${code.code}`
  })
}

const handleRedeemCodeAction = (command: string, code: RedeemCode) => {
  switch (command) {
    case 'view':
      handleViewRedeemCode(code)
      break
    case 'edit':
      handleEditRedeemCode(code)
      break
    case 'toggle':
      ElMessage({
        type: 'info',
        message: `${code.is_active ? '禁用' : '激活'}兑换码: ${code.code}`
      })
      break
    case 'delete':
      handleDeleteRedeemCode(code)
      break
  }
}

const handleCreateRedeemCode = () => {
  showCreateRedeemCode.value = true
}

const handleEditRedeemCode = (code: RedeemCode) => {
  selectedRedeemCode.value = code
  showEditRedeemCode.value = true
}

const handleDeleteRedeemCode = async (code: RedeemCode) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除兑换码 "${code.code}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await subscriptionStore.deleteRedeemCode(code.id)
    ElMessage.success('兑换码删除成功')
    loadRedeemCodes()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除兑换码失败:', error)
      ElMessage.error(error.message || '删除兑换码失败')
    }
  }
}

const handleRedeemCodeCreated = () => {
  showCreateRedeemCode.value = false
  loadRedeemCodes()
}

const handleRedeemCodeUpdated = () => {
  showEditRedeemCode.value = false
  selectedRedeemCode.value = null
  loadRedeemCodes()
}

const handleRedeemCodePageChange = (page: number) => {
  redeemCodePagination.page = page
  loadRedeemCodes()
}

// 兑换记录相关方法
const loadRedemptions = async () => {
  const params = {
    page: redemptionPagination.page,
    page_size: redemptionPagination.page_size
  }
  const response = await subscriptionStore.fetchRedemptions(params)
  redemptions.value = response.items
  redemptionPagination.total = response.total
  redemptionPagination.pages = response.pages
}

const handleRedemptionSearch = async (params: any) => {
  const response = await subscriptionStore.fetchRedemptions(params)
  redemptions.value = response.items
  redemptionPagination.total = response.total
  redemptionPagination.pages = response.pages
  redemptionPagination.page = params.page || 1
  redemptionPagination.page_size = params.page_size || 25
}

const handleRedemptionRefresh = () => {
  loadRedemptions()
}

const handleViewRedemption = (redemption: RedeemCodeRedemption) => {
  ElMessage({
    type: 'info',
    message: `查看兑换记录详情: ${redemption.redeem_code}`
  })
}

const handleRedemptionAction = (command: string, redemption: RedeemCodeRedemption) => {
  switch (command) {
    case 'view':
      handleViewRedemption(redemption)
      break
    case 'export':
      ElMessage({
        type: 'info',
        message: `导出兑换记录: ${redemption.redeem_code}`
      })
      break
  }
}

// 将兑换记录转换为getUserDisplayName需要的格式
const getUserDisplayNameForRedemption = (redemption: RedeemCodeRedemption): string => {
  const userInfo = {
    username: redemption.user_username || '',
    email: redemption.user_email || '',
    wechat_nickname: redemption.user_wechat_nickname || null,
    nickname: redemption.user_nickname || null
  } as any
  
  return getUserDisplayName(userInfo)
}

// 积分账户相关方法
const loadCreditAccounts = async () => {
  const params = {
    page: creditAccountPagination.page,
    page_size: creditAccountPagination.page_size
  }
  const response = await subscriptionStore.fetchCreditAccounts(params)
  creditAccounts.value = response.items
  creditAccountPagination.total = response.total
}

const handleCreditAccountPageChange = (page: number) => {
  creditAccountPagination.page = page
  loadCreditAccounts()
}

// 积分交易相关方法
const loadCreditTransactions = async () => {
  const params = {
    page: creditTransactionPagination.page,
    page_size: creditTransactionPagination.page_size
  }
  const response = await subscriptionStore.fetchCreditTransactions(params)
  creditTransactions.value = response.items
  creditTransactionPagination.total = response.total
}

const handleCreditTransactionPageChange = (page: number) => {
  creditTransactionPagination.page = page
  loadCreditTransactions()
}

// 初始化
onMounted(async () => {
  try {
    // 加载统计数据
    await subscriptionStore.fetchStats()
    // 加载当前标签页数据
    await loadTabData(activeTab.value)
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage({
      type: 'error',
      message: '初始化失败'
    })
  }
})
</script>

<style scoped>
.subscription-view {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
}

.stats-card-section {
  margin-bottom: 64px;
}

.subscription-tabs-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.subscription-tabs-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgba(226, 232, 240, 0.6);
}

.subscription-tabs {
  background: transparent;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 24px 24px 0;
  background: transparent;
}

:deep(.el-tabs__content) {
  padding: 0 24px 24px 24px;
}

:deep(.el-tabs__item) {
  font-weight: 500;
  font-size: 16px;
  padding: 0 24px;
  height: 52px;
  line-height: 52px;
  border-radius: 8px 8px 0 0;
  margin-right: 4px;
  transition: all 0.3s ease;
}

:deep(.el-tabs__item.is-active) {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  font-weight: 600;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(226, 232, 240, 0.6);
}

.redeem-codes-section {
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  padding: 24px;
  margin: 16px 0 0 0;
  border: 1px solid rgba(226, 232, 240, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subscription-view {
    padding: 16px;
  }
  
  .subscription-tabs-container {
    border-radius: 12px;
  }
  
  :deep(.el-tabs__header) {
    padding: 16px 16px 0;
  }
  
  :deep(.el-tabs__content) {
    padding: 0 16px 16px 16px;
  }
  
  :deep(.el-tabs__item) {
    padding: 0 16px;
    font-size: 15px;
    height: 48px;
    line-height: 48px;
  }
  
  .redeem-codes-section {
    padding: 16px;
    margin: 12px 0 0 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .subscription-tabs-container {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: rgba(71, 85, 105, 0.3);
  }
  
  .subscription-tabs-container:hover {
    border-color: rgba(71, 85, 105, 0.5);
  }
  
  :deep(.el-tabs__item.is-active) {
    background: rgba(59, 130, 246, 0.15);
  }
  
  :deep(.el-tabs__nav-wrap::after) {
    background-color: rgba(71, 85, 105, 0.4);
  }
  
  .redeem-codes-section {
    background: rgba(30, 41, 59, 0.3);
    border-color: rgba(71, 85, 105, 0.3);
  }
}
</style> 