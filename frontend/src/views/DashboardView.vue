<template>
  <div class="dashboard">
    <!-- 第一层：总览卡片 -->
    <div class="stats-section">
      <h2 class="section-title">总览</h2>
      <div class="stats-grid">
        <StatsCard
          title="总注册用户"
          :value="dashboardStore.stats?.total_users || 0"
          icon="UserFilled"
          color="#3b82f6"
          suffix="人"
        />
        
        <StatsCard
          title="今日新增用户"
          :value="dashboardStore.stats?.today_new_users || 0"
          :growth="dashboardStore.stats?.today_growth_rate"
          icon="TrendCharts"
          color="#10b981"
          suffix="人"
        />
      </div>
      
      <!-- 活跃用户指标 -->
      <div class="stats-grid stats-secondary">
        <StatsCard
          title="昨日活跃用户"
          :value="dashboardStore.stats?.yesterday_active_users || 0"
          icon="Timer"
          color="#f59e0b"
          suffix="人"
        />
        
        <StatsCard
          title="近7日活跃用户"
          :value="dashboardStore.stats?.week_active_users || 0"
          icon="Calendar"
          color="#8b5cf6"
          suffix="人"
        />
        
        <StatsCard
          title="近30日活跃用户"
          :value="dashboardStore.stats?.month_active_users || 0"
          icon="DataAnalysis"
          color="#06b6d4"
          suffix="人"
        />
      </div>
    </div>

    <!-- 第二层：数据分析图表 -->
    <div class="charts-section">
      <h2 class="section-title">数据分析图表</h2>
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">用户增长趋势</h3>
            <div class="chart-controls">
                      <el-radio-group v-model="chartDays" @change="handleDaysChange" size="small">
          <el-radio-button :value="7">近7天</el-radio-button>
          <el-radio-button :value="30">近30天</el-radio-button>
          <el-radio-button :value="90">近90天</el-radio-button>
        </el-radio-group>
            </div>
          </div>
          <div class="chart-container">
            <UserActivityChart :data="dashboardStore.userActivity" />
          </div>
        </div>
      </div>
    </div>

    <!-- 第三层：最近用户列表 -->
    <div class="users-section">
      <h2 class="section-title">最近用户列表</h2>
      <div class="users-card">
        <div class="users-header">
          <h3 class="users-title">最近用户列表</h3>
        </div>
        
        <div class="users-table">
          <RecentUsersTable 
            :users="dashboardStore.recentUsers" 
            :loading="dashboardStore.loading"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import StatsCard from '@/components/dashboard/StatsCard.vue'
import UserActivityChart from '@/components/dashboard/UserActivityChart.vue'
import RecentUsersTable from '@/components/dashboard/RecentUsersTable.vue'

const dashboardStore = useDashboardStore()

// 图表控制
const chartDays = ref(30)

// 处理天数变化
const handleDaysChange = async (days: number) => {
  await dashboardStore.fetchUserActivity(days)
}

// 初始化数据
onMounted(async () => {
  try {
    await dashboardStore.initDashboard()
  } catch (error) {
    console.error('DashboardView: 初始化失败', error)
  }
})
</script>

<style scoped>
.dashboard {
  max-width: 1400px;
  margin: 0 auto;
  space-y: 32px;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 24px;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #0f172a 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 第一层：统计卡片 */
.stats-section {
  margin-bottom: 64px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stats-secondary {
  margin-top: 24px;
}

/* 第二层：图表区域 */
.charts-section {
  margin-bottom: 64px;
}

.charts-grid {
  display: grid;
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-container {
  height: 400px;
  position: relative;
}

/* 第三层：用户管理 */
.users-section {
  margin-bottom: 32px;
}

.users-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.users-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.users-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.users-table {
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .chart-header,
  .users-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .chart-controls,
  .users-controls {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .dashboard {
    space-y: 24px;
  }
  
  .section-title {
    font-size: 20px;
    margin-bottom: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .chart-card,
  .users-card {
    padding: 20px;
  }
  
  .chart-container {
    height: 320px;
  }
  
  .users-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .users-controls .el-input {
    width: 100% !important;
  }
}
</style>