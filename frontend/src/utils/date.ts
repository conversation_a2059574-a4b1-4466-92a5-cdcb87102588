/**
 * 日期时间格式化工具函数
 */

/**
 * 标准化日期时间字符串，确保月份和日期是两位数
 * @param dateTimeStr - 日期时间字符串
 * @returns 标准化后的字符串
 */
const normalizeDateTime = (dateTimeStr: string): string => {
  // 匹配格式：YYYY-M-D[THH:mm:ss]
  const match = dateTimeStr.match(/^(\d{4})-(\d{1,2})-(\d{1,2})(.*)$/)
  if (!match) return dateTimeStr
  
  const [, year, month, day, timePart] = match
  const paddedMonth = month.padStart(2, '0')
  const paddedDay = day.padStart(2, '0')
  
  return `${year}-${paddedMonth}-${paddedDay}${timePart}`
}

/**
 * 格式化日期时间为 YYYY-M-D HH:mm 格式（北京时间 UTC+8）
 * @param dateStr - ISO日期字符串或Date对象
 * @returns 格式化后的日期字符串，如 "2025-7-27 15:00"
 */
export const formatDateTime = (dateStr: string | Date | null | undefined): string => {
  if (!dateStr) return '-'
  
  let date: Date
  
  if (typeof dateStr === 'string') {
    // 智能处理不同的时间格式
    let processedDateStr = dateStr
    
    // 如果字符串没有时区信息，假设它是UTC时间并添加Z
    if (!dateStr.includes('Z') && !dateStr.includes('+') && !dateStr.includes('-', 10)) {
      // 检查是否是ISO格式但没有时区标识
      if (dateStr.includes('T')) {
        // 标准化日期格式 - 确保月份和日期是两位数
        processedDateStr = normalizeDateTime(dateStr) + 'Z'
      } else if (dateStr.match(/^\d{4}-\d{1,2}-\d{1,2} \d{1,2}:\d{1,2}(:\d{1,2})?$/)) {
        // 处理 "YYYY-MM-DD HH:mm:ss" 格式，假设为UTC
        const normalizedDateTime = normalizeDateTime(dateStr.replace(' ', 'T'))
        processedDateStr = normalizedDateTime + 'Z'
      }
    }
    
    date = new Date(processedDateStr)
  } else {
    date = dateStr
  }
  
  if (isNaN(date.getTime())) {
    console.warn('时间解析失败:', dateStr)
    return '-'
  }
  
  // 使用北京时区格式化
  try {
    const beijingTime = new Intl.DateTimeFormat('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).formatToParts(date)
    
    const year = beijingTime.find(part => part.type === 'year')?.value || ''
    const month = beijingTime.find(part => part.type === 'month')?.value || ''
    const day = beijingTime.find(part => part.type === 'day')?.value || ''
    const hour = beijingTime.find(part => part.type === 'hour')?.value || ''
    const minute = beijingTime.find(part => part.type === 'minute')?.value || ''
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  } catch (error) {
    // 降级处理：手动计算UTC+8
    console.warn('时区格式化失败，使用降级方案:', error)
    const utcTime = date.getTime()
    const beijingOffset = 8 * 60 * 60 * 1000 // 8小时毫秒数
    const beijingTime = new Date(utcTime + beijingOffset)
    
    const year = beijingTime.getUTCFullYear()
    const month = beijingTime.getUTCMonth() + 1
    const day = beijingTime.getUTCDate()
    const hours = beijingTime.getUTCHours().toString().padStart(2, '0')
    const minutes = beijingTime.getUTCMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }
}

/**
 * 格式化日期为 YYYY-M-D 格式（北京时间 UTC+8）
 * @param dateStr - ISO日期字符串或Date对象
 * @returns 格式化后的日期字符串，如 "2025-7-27"
 */
export const formatDate = (dateStr: string | Date | null | undefined): string => {
  if (!dateStr) return '-'
  
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr
  
  if (isNaN(date.getTime())) return '-'
  
  // 使用北京时区格式化
  try {
    const beijingTime = new Intl.DateTimeFormat('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: 'numeric', 
      day: 'numeric'
    }).formatToParts(date)
    
    const year = beijingTime.find(part => part.type === 'year')?.value || ''
    const month = beijingTime.find(part => part.type === 'month')?.value || ''
    const day = beijingTime.find(part => part.type === 'day')?.value || ''
    
    return `${year}-${month}-${day}`
  } catch (error) {
    // 降级处理
    const utcTime = date.getTime()
    const beijingOffset = 8 * 60 * 60 * 1000
    const beijingTime = new Date(utcTime + beijingOffset)
    
    const year = beijingTime.getUTCFullYear()
    const month = beijingTime.getUTCMonth() + 1
    const day = beijingTime.getUTCDate()
    
    return `${year}-${month}-${day}`
  }
}

/**
 * 检查日期是否为今天（基于北京时间）
 * @param dateStr - ISO日期字符串或Date对象
 * @returns 是否为今天
 */
export const isToday = (dateStr: string | Date | null | undefined): boolean => {
  if (!dateStr) return false
  
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr
  const now = new Date()
  
  // 使用北京时区比较
  try {
    const formatOptions: Intl.DateTimeFormatOptions = { 
      timeZone: 'Asia/Shanghai', 
      year: 'numeric', 
      month: 'numeric', 
      day: 'numeric' 
    }
    const dateStr = new Intl.DateTimeFormat('zh-CN', formatOptions).format(date)
    const todayStr = new Intl.DateTimeFormat('zh-CN', formatOptions).format(now)
    
    return dateStr === todayStr
  } catch (error) {
    // 降级处理
    const beijingDate = new Date(date.getTime() + (8 * 60 * 60 * 1000))
    const beijingToday = new Date(now.getTime() + (8 * 60 * 60 * 1000))
    
    return (
      beijingDate.getUTCFullYear() === beijingToday.getUTCFullYear() &&
      beijingDate.getUTCMonth() === beijingToday.getUTCMonth() &&
      beijingDate.getUTCDate() === beijingToday.getUTCDate()
    )
  }
}

/**
 * 获取相对时间描述
 * @param dateStr - ISO日期字符串或Date对象
 * @returns 相对时间描述，如 "刚刚"、"5分钟前"、"今天"、"昨天"等
 */
export const getRelativeTime = (dateStr: string | Date | null | undefined): string => {
  if (!dateStr) return '-'
  
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24 && isToday(date)) {
    return `${diffHours}小时前`
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return formatDate(date)
  }
} 