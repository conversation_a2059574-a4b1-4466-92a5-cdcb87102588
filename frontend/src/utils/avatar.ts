/**
 * 头像工具函数
 */

import type { RecentUser } from '@/types'

/**
 * 获取用户显示名称
 * @param user - 用户对象
 * @returns 用户显示名称
 */
export const getUserDisplayName = (user: RecentUser): string => {
  return user.wechat_nickname || user.nickname || user.username || '未知用户'
}

/**
 * 获取显示邮箱（过滤临时邮箱）
 * @param email - 邮箱地址
 * @returns 显示的邮箱或占位符
 */
export const getDisplayEmail = (email?: string): string => {
  if (!email) return '-'
  // 过滤微信临时邮箱
  if (email.endsWith('@wechat.temp')) return '-'
  return email
}

/**
 * 获取头像显示文本（第一个字符）
 * @param user - 用户对象或用户名字符串
 * @returns 头像显示文本
 */
export const getAvatarText = (user: RecentUser | string): string => {
  let name = ''
  
  if (typeof user === 'string') {
    name = user
  } else {
    name = getUserDisplayName(user)
  }
  
  // 获取第一个字符（支持中文、英文、emoji）
  return name.charAt(0).toUpperCase()
}

/**
 * 根据用户名生成一致的颜色
 * @param username - 用户名
 * @param colors - 颜色数组
 * @returns 颜色值
 */
export const generateAvatarColor = (username: string, colors: string[]): string => {
  if (!username || colors.length === 0) return '#e5e7eb'
  
  // 根据用户名生成一致的索引
  const index = username.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length
  return colors[index]
}

/**
 * 基于登录方式的头像颜色配色方案
 */
export const AVATAR_COLORS = {
  // 根据登录方式的基础颜色
  loginType: {
    'password': '#6366f1',  // 靛蓝色 - 密码登录
    'wechat': '#10b981',    // 绿色 - 微信登录  
    'phone': '#f59e0b',     // 橙色 - 手机登录
    'email': '#3b82f6',     // 蓝色 - 邮箱登录
  },
  
  // 根据登录方式的渐变颜色
  gradients: {
    'password': 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',  // 靛蓝到紫色
    'wechat': 'linear-gradient(135deg, #10b981 0%, #34d399 100%)',    // 绿色渐变
    'phone': 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',     // 橙色渐变
    'email': 'linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%)',     // 蓝色渐变
  },
  
  // 柔和版本
  soft: {
    'password': '#a78bfa',  // 柔和紫色
    'wechat': '#34d399',    // 柔和绿色
    'phone': '#fbbf24',     // 柔和橙色
    'email': '#60a5fa',     // 柔和蓝色
  }
}

/**
 * 获取用户头像配置（基于登录方式）
 * @param user - 用户对象
 * @param variant - 样式变体
 * @returns 头像配置对象
 */
export const getAvatarConfig = (user: RecentUser, variant: 'loginType' | 'gradient' | 'soft' = 'loginType') => {
  const displayName = getUserDisplayName(user)
  const avatarText = getAvatarText(user)
  const loginType = user.login_type || 'password'
  
  let backgroundColor = ''
  let background = ''
  
  switch (variant) {
    case 'gradient':
      background = AVATAR_COLORS.gradients[loginType as keyof typeof AVATAR_COLORS.gradients] || AVATAR_COLORS.gradients.password
      break
    case 'soft':
      backgroundColor = AVATAR_COLORS.soft[loginType as keyof typeof AVATAR_COLORS.soft] || AVATAR_COLORS.soft.password
      break
    default:
      backgroundColor = AVATAR_COLORS.loginType[loginType as keyof typeof AVATAR_COLORS.loginType] || AVATAR_COLORS.loginType.password
  }
  
  return {
    text: avatarText,
    displayName,
    backgroundColor,
    background,
    tooltip: `${displayName} (${user.email})`
  }
} 