import { ref, reactive } from 'vue'
import { defineStore } from 'pinia'
import request from '@/utils/request'
import type { UserDetail, UserStats, UserFilters, PaginatedResponse } from '@/types'

export const useUserManagementStore = defineStore('userManagement', () => {
  // 状态
  const loading = ref(false)
  const stats = ref<UserStats | null>(null)
  const users = ref<UserDetail[]>([])
  const pagination = reactive({
    page: 1,
    page_size: 25,
    total: 0,
    pages: 0
  })
  
  // 筛选条件
  const filters = reactive<UserFilters>({
    search: '',
    role: undefined,
    is_active: undefined,
    login_type: undefined,
    sort_by: 'created_at',
    sort_order: 'desc',
    start_date: undefined,
    end_date: undefined
  })

  // 获取用户统计信息
  const fetchUserStats = async () => {
    try {
      loading.value = true
      const response = await request.get('/user-management/stats')
      console.log('用户统计API响应:', response)
      stats.value = response as unknown as UserStats
    } catch (error) {
      console.error('获取用户统计失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 25) => {
    try {
      loading.value = true
      const params = {
        page,
        page_size: pageSize,
        ...filters
      }
      
      // 清理空值参数
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params]
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof typeof params]
        }
      })

      const response = await request.get('/user-management/users', {
        params
      })
      
      console.log('用户列表API响应:', response)
      const data = response as unknown as PaginatedResponse<UserDetail>
      console.log('处理后的数据:', data)
      
      users.value = data.items
      pagination.page = data.page
      pagination.page_size = data.page_size
      pagination.total = data.total
      pagination.pages = data.pages
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户详情
  const fetchUserDetail = async (userId: number): Promise<UserDetail> => {
    try {
      const response = await request.get(`/user-management/users/${userId}`)
      return response as unknown as UserDetail
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw error
    }
  }

  // 搜索用户
  const searchUsers = async (searchQuery: string) => {
    filters.search = searchQuery
    pagination.page = 1
    await fetchUsers(1, pagination.page_size)
  }

  // 应用筛选条件
  const applyFilters = async (newFilters: Partial<UserFilters>) => {
    Object.assign(filters, newFilters)
    pagination.page = 1
    await fetchUsers(1, pagination.page_size)
  }

  // 重置筛选条件
  const resetFilters = async () => {
    Object.assign(filters, {
      search: '',
      role: undefined,
      is_active: undefined,
      login_type: undefined,
      sort_by: 'created_at',
      sort_order: 'desc',
      start_date: undefined,
      end_date: undefined
    })
    pagination.page = 1
    await fetchUsers(1, pagination.page_size)
  }

  // 更改页面
  const changePage = async (page: number) => {
    await fetchUsers(page, pagination.page_size)
  }

  // 更改页面大小
  const changePageSize = async (pageSize: number) => {
    pagination.page = 1
    await fetchUsers(1, pageSize)
  }

  // 刷新数据
  const refresh = async () => {
    await Promise.all([
      fetchUserStats(),
      fetchUsers(pagination.page, pagination.page_size)
    ])
  }

  return {
    // 状态
    loading,
    stats,
    users,
    pagination,
    filters,
    
    // 方法
    fetchUserStats,
    fetchUsers,
    fetchUserDetail,
    searchUsers,
    applyFilters,
    resetFilters,
    changePage,
    changePageSize,
    refresh
  }
}) 