import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { DashboardStats, UserActivity, RecentUser } from '@/types'
import request from '@/utils/request'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const stats = ref<DashboardStats | null>(null)
  const userActivity = ref<UserActivity[]>([])
  const recentUsers = ref<RecentUser[]>([])
  const loading = ref(false)

  // 获取核心统计数据
  const fetchStats = async (): Promise<void> => {
    loading.value = true
    try {
      const data: DashboardStats = await request.get('/dashboard/stats')
      stats.value = data
    } finally {
      loading.value = false
    }
  }

  // 获取用户活动趋势数据
  const fetchUserActivity = async (days: number = 30): Promise<void> => {
    try {
      const data: UserActivity[] = await request.get('/dashboard/user-activity', {
        params: { days }
      })
      userActivity.value = data
    } catch (error) {
      console.error('获取用户活动数据失败:', error)
      userActivity.value = []
    }
  }

  // 获取最近用户列表
  const fetchRecentUsers = async (limit: number = 20): Promise<void> => {
    try {
      const data: RecentUser[] = await request.get('/dashboard/recent-users', {
        params: { limit }
      })
      recentUsers.value = data
    } catch (error) {
      console.error('获取最近用户列表失败:', error)
    }
  }

  // 搜索用户
  const searchUsers = async (query: string, limit: number = 20): Promise<RecentUser[]> => {
    try {
      const data: RecentUser[] = await request.get('/dashboard/user-search', {
        params: { query, limit }
      })
      return data
    } catch (error) {
      console.error('搜索用户失败:', error)
      return []
    }
  }

  // 初始化数据
  const initDashboard = async (): Promise<void> => {
    await Promise.all([
      fetchStats(),
      fetchUserActivity(),
      fetchRecentUsers(),
    ])
  }

  // 刷新所有数据
  const refreshAll = async (): Promise<void> => {
    await initDashboard()
  }

  return {
    // 状态
    stats,
    userActivity,
    recentUsers,
    loading,
    // 方法
    fetchStats,
    fetchUserActivity,
    fetchRecentUsers,
    searchUsers,
    initDashboard,
    refreshAll,
  }
})