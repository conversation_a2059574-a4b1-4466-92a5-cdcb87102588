import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, AuthToken } from '@/types'
import request from '@/utils/request'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('bms_token'))
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => {
    return user.value?.role === 'admin' || user.value?.role === 'super_admin'
  })

  // 登录方法
  const login = async (credentials: LoginForm): Promise<void> => {
    loading.value = true
    try {
      const response: AuthToken = await request.post('/auth/login', credentials)
      
      // 保存token
      token.value = response.access_token
      localStorage.setItem('bms_token', response.access_token)
      
      // 获取用户信息
      await getCurrentUser()
    } finally {
      loading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<void> => {
    if (!token.value) return
    
    try {
      const userData: User = await request.get('/auth/me')
      user.value = userData
    } catch (error) {
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    }
  }

  // 登出方法
  const logout = (): void => {
    token.value = null
    user.value = null
    localStorage.removeItem('bms_token')
  }

  // 刷新token方法
  const refreshToken = async (): Promise<void> => {
    try {
      const response: AuthToken = await request.post('/auth/refresh')
      token.value = response.access_token
      localStorage.setItem('bms_token', response.access_token)
    } catch (error) {
      logout()
      throw error
    }
  }

  // 初始化：如果有token，获取用户信息
  const init = async (): Promise<void> => {
    if (token.value) {
      try {
        await getCurrentUser()
      } catch {
        logout()
      }
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    // 计算属性
    isAuthenticated,
    isAdmin,
    // 方法
    login,
    logout,
    getCurrentUser,
    refreshToken,
    init,
  }
})