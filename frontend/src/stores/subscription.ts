import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { 
  PaymentOrder, RedeemCode, RedeemCodeCreate, RedeemCodeRedemption,
  UserCreditAccount, CreditTransaction, SubscriptionStats,
  PaginatedResponse
} from '@/types'
import request from '@/utils/request'

export const useSubscriptionStore = defineStore('subscription', () => {
  // 状态
  const loading = ref(false)
  const stats = ref<SubscriptionStats | null>(null)
  
  // 支付订单管理
  const fetchPaymentOrders = async (params: any = {}): Promise<PaginatedResponse<PaymentOrder>> => {
    loading.value = true
    try {
      const response = await request.get('/subscription/orders', { params })
      return response as any
    } finally {
      loading.value = false
    }
  }

  const fetchPaymentOrder = async (orderId: number): Promise<PaymentOrder> => {
    loading.value = true
    try {
      const response = await request.get(`/subscription/orders/${orderId}`)
      return response as any
    } finally {
      loading.value = false
    }
  }

  const updatePaymentOrder = async (orderId: number, data: any): Promise<PaymentOrder> => {
    loading.value = true
    try {
      const response = await request.put(`/subscription/orders/${orderId}`, data)
      return response as any
    } finally {
      loading.value = false
    }
  }

  // 兑换码管理
  const fetchRedeemCodes = async (params: any = {}): Promise<PaginatedResponse<RedeemCode>> => {
    loading.value = true
    try {
      const response = await request.get('/subscription/redeem-codes', { params })
      return response as any
    } finally {
      loading.value = false
    }
  }

  const createRedeemCode = async (data: RedeemCodeCreate): Promise<RedeemCode> => {
    loading.value = true
    try {
      const response = await request.post('/subscription/redeem-codes', data)
      return response as any
    } finally {
      loading.value = false
    }
  }

  const updateRedeemCode = async (codeId: number, data: any): Promise<RedeemCode> => {
    loading.value = true
    try {
      const response = await request.put(`/subscription/redeem-codes/${codeId}`, data)
      return response as any
    } finally {
      loading.value = false
    }
  }

  const createCustomPackageRedeemCode = async (data: any): Promise<any> => {
    loading.value = true
    try {
      const response = await request.post('/subscription/custom-package-redeem-code', data)
      return response as any
    } finally {
      loading.value = false
    }
  }

  const deleteRedeemCode = async (codeId: number): Promise<void> => {
    loading.value = true
    try {
      await request.delete(`/subscription/redeem-codes/${codeId}`)
    } finally {
      loading.value = false
    }
  }

  // 兑换记录管理
  const fetchRedemptions = async (params: any = {}): Promise<PaginatedResponse<RedeemCodeRedemption>> => {
    loading.value = true
    try {
      const response = await request.get('/subscription/redemptions', { params })
      return response as any
    } finally {
      loading.value = false
    }
  }

  // 用户积分账户管理
  const fetchCreditAccounts = async (params: any = {}): Promise<PaginatedResponse<UserCreditAccount>> => {
    loading.value = true
    try {
      const response = await request.get('/subscription/credit-accounts', { params })
      return response as any
    } finally {
      loading.value = false
    }
  }

  // 积分交易记录
  const fetchCreditTransactions = async (params: any = {}): Promise<PaginatedResponse<CreditTransaction>> => {
    loading.value = true
    try {
      const response = await request.get('/subscription/credit-transactions', { params })
      return response as any
    } finally {
      loading.value = false
    }
  }

  // 订阅统计
  const fetchStats = async (): Promise<void> => {
    loading.value = true
    try {
      const response = await request.get('/subscription/stats')
      // 将字符串数字转换为数字类型
      const rawStats = response as any
      stats.value = {
        total_orders: Number(rawStats.total_orders) || 0,
        pending_orders: Number(rawStats.pending_orders) || 0,
        paid_orders: Number(rawStats.paid_orders) || 0,
        total_revenue: Number(rawStats.total_revenue) || 0,
        total_credits_sold: Number(rawStats.total_credits_sold) || 0,
        active_redeem_codes: Number(rawStats.active_redeem_codes) || 0,
        total_redemptions: Number(rawStats.total_redemptions) || 0,
        // 增长率数据
        orders_growth_rate: rawStats.orders_growth_rate !== undefined ? Number(rawStats.orders_growth_rate) : undefined,
        revenue_growth_rate: rawStats.revenue_growth_rate !== undefined ? Number(rawStats.revenue_growth_rate) : undefined,
        credits_growth_rate: rawStats.credits_growth_rate !== undefined ? Number(rawStats.credits_growth_rate) : undefined,
        redemptions_growth_rate: rawStats.redemptions_growth_rate !== undefined ? Number(rawStats.redemptions_growth_rate) : undefined
      }
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    loading,
    stats,

    // 方法
    fetchPaymentOrders,
    fetchPaymentOrder,
    updatePaymentOrder,
    fetchRedeemCodes,
    createRedeemCode,
    updateRedeemCode,
    deleteRedeemCode,
    createCustomPackageRedeemCode,
    fetchRedemptions,
    fetchCreditAccounts,
    fetchCreditTransactions,
    fetchStats
  }
}) 