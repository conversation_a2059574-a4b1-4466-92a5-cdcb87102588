<template>
  <div class="users-table-container">
    <el-table
      :data="paginatedUsers"
      v-loading="loading"
      style="width: 100%"
      :height="400"
      empty-text="暂无用户数据"
      header-row-class-name="table-header"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="用户信息" min-width="200">
        <template #default="{ row }">
          <div class="user-info">
            <UserAvatar 
              :user="row" 
              size="medium" 
              :variant="getAvatarVariant(row)"
              class="user-avatar"
            />
            <div class="user-details">
              <div class="username">{{ getUserDisplayName(row) }}</div>
              <div class="email">{{ getDisplayEmail(row.email) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="登录方式" width="120" align="center">
        <template #default="{ row }">
          <el-tag
            :type="getLoginTypeTagType(row.login_type)"
            size="small"
            effect="light"
          >
            {{ getLoginTypeText(row.login_type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="最后登录" width="160" align="center">
        <template #default="{ row }">
          <div v-if="row.last_login" class="time-info">
            <el-icon class="time-icon"><Clock /></el-icon>
            <span>{{ formatDateTime(row.last_login) }}</span>
          </div>
          <span v-else class="no-data">未登录</span>
        </template>
      </el-table-column>
      
      <el-table-column label="注册时间" width="160" align="center">
        <template #default="{ row }">
          <div v-if="row.created_at" class="time-info">
            <el-icon class="time-icon"><Calendar /></el-icon>
            <span>{{ formatDateTime(row.created_at) }}</span>
          </div>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div v-if="total > pageSize" class="pagination-container flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="25"
        :total="total"
        layout="total, prev, pager, next"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { RecentUser } from '@/types'
import { formatDateTime } from '@/utils/date'
import { getUserDisplayName, getDisplayEmail } from '@/utils/avatar'
import UserAvatar from '@/components/common/UserAvatar.vue'

interface Props {
  users: RecentUser[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 移除了refresh事件，用户操作统一在用户管理界面进行

// 分页相关
const currentPage = ref(1)
const pageSize = 25
const total = computed(() => props.users.length)

// 分页数据
const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return props.users.slice(start, end)
})

// 注：getUserDisplayName 函数已从 @/utils/avatar 导入

// 根据用户类型获取头像样式变体
const getAvatarVariant = (user: RecentUser): 'default' | 'colorful' | 'gradient' | 'premium' => {
  // VIP用户或管理员使用premium样式
  if (user.login_type === 'wechat' && user.wechat_nickname) {
    return 'premium'
  }
  // 有昵称的用户使用渐变样式
  if (user.nickname || user.wechat_nickname) {
    return 'gradient'
  }
  // 普通用户使用彩色样式
  return 'colorful'
}

// 获取登录方式文本
const getLoginTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'password': '密码',
    'wechat': '微信',
    'phone': '手机',
    'email': '邮箱'
  }
  return typeMap[type] || '未知'
}

// 获取登录方式标签类型
const getLoginTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'password': 'info',
    'wechat': 'success',
    'phone': 'warning',
    'email': 'primary'
  }
  return typeMap[type] || 'info'
}

// 移除了操作功能，用户操作统一在用户管理界面进行

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page
}

// 页面大小固定为25，不需要处理页面大小变化
</script>

<style scoped>
.users-table-container {
  width: 100%;
}

/* 表格样式 */
:deep(.table-header) {
  background-color: #f8fafc;
}

:deep(.table-header th) {
  background-color: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #f8fafc !important;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  /* 头像组件已有自己的样式，无需额外边框和阴影 */
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 600;
  color: #1a202c;
  font-size: 14px;
}

.email {
  font-size: 12px;
  color: #6b7280;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
}

.time-icon {
  font-size: 14px;
}

.no-data {
  color: #9ca3af;
  font-size: 12px;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    :deep(.el-pagination) {
      flex-wrap: wrap;
      justify-content: center;
    }
    
    :deep(.el-pagination__total),
    :deep(.el-pagination__sizes) {
      margin-bottom: 8px;
    }
  }
}
</style>