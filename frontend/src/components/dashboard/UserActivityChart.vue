<template>
  <div class="chart-wrapper">
    <div v-show="loading" class="chart-loading">
      <el-icon class="loading-icon" :size="32">
        <Loading />
      </el-icon>
      <p class="loading-text">加载图表数据...</p>
    </div>
    <div ref="chartRef" class="chart-container" :class="{ 'chart-hidden': loading }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { init, type EChartsOption } from 'echarts'
import type { UserActivity } from '@/types'

interface Props {
  data: UserActivity[]
}

const props = defineProps<Props>()

const chartRef = ref<HTMLElement>()
const loading = ref(false) // 改为false，避免阻止DOM渲染
let chart: any = null

// Observer 实例
let resizeObserver: ResizeObserver | null = null
let intersectionObserver: IntersectionObserver | null = null
let initRetryCount = 0
const maxRetryCount = 20

// 设置响应式图表
const setupResponsiveChart = () => {
  if (!chartRef.value || !chart) return
  
  // 清理旧的监听器
  cleanupResponsiveChart()
  
  // 监听窗口大小变化
  const handleWindowResize = () => {
    chart?.resize()
  }
  window.addEventListener('resize', handleWindowResize)
  
  // 使用 ResizeObserver 监听容器尺寸变化
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        if (width > 0 && height > 0 && chart) {
          chart.resize()
        }
      }
    })
    resizeObserver.observe(chartRef.value)
  }
  
  // 存储清理函数
  chart._cleanup = () => {
    window.removeEventListener('resize', handleWindowResize)
    cleanupObservers()
  }
}

// 清理响应式监听
const cleanupResponsiveChart = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}

// 清理所有观察器
const cleanupObservers = () => {
  cleanupResponsiveChart()
  if (intersectionObserver) {
    intersectionObserver.disconnect()
    intersectionObserver = null
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) {
    return
  }
  
  if (!props.data || !props.data.length) {
    return
  }
  
  // 确保容器有尺寸
  const containerWidth = chartRef.value.clientWidth
  const containerHeight = chartRef.value.clientHeight
  
  if (containerWidth === 0 || containerHeight === 0) {
    if (initRetryCount < maxRetryCount) {
      initRetryCount++
      // 再次强制设置容器尺寸
      forceContainerSize()
      
      // 使用递增的延迟时间
      const delay = Math.min(200 * initRetryCount, 3000)
      setTimeout(() => initChart(), delay)
    } else {
      console.error('UserActivityChart: 超过最大重试次数，强制使用固定尺寸初始化')
      // 最后的尝试：强制使用固定尺寸
      chartRef.value.style.width = '100%'
      chartRef.value.style.height = '400px'
      // 继续初始化，即使尺寸为0
    }
    
    if (initRetryCount < maxRetryCount) {
      return
    }
  }
  
  // 重置重试计数
  initRetryCount = 0
  loading.value = true
  
  await nextTick()
  
  try {
      // 销毁已存在的图表
  if (chart) {
    chart.dispose()
  }
  
  // 创建新图表
  chart = init(chartRef.value)
    
    // 准备数据
    const dates = props.data.map(item => {
      const date = new Date(item.date)
      return `${date.getMonth() + 1}/${date.getDate()}`
    })
    const newUsers = props.data.map(item => item.new_users)
    const activeUsers = props.data.map(item => item.active_users)
    
    // 配置选项
    const option: EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: (params: any) => {
          let tooltip = `<div style="font-weight: 600; margin-bottom: 8px;">${params[0].axisValue}</div>`
          params.forEach((param: any) => {
            tooltip += `
              <div style="display: flex; align-items: center; margin-bottom: 4px;">
                <div style="width: 12px; height: 12px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></div>
                <span style="color: #666;">${param.seriesName}:</span>
                <span style="margin-left: 8px; font-weight: 600;">${param.value}人</span>
              </div>
            `
          })
          return tooltip
        }
      },
      legend: {
        data: ['新增用户', '活跃用户'],
        top: 10,
        itemGap: 20,
        textStyle: {
          color: '#666',
          fontSize: 14
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 12,
          formatter: (value: number) => {
            if (value >= 1000) {
              return (value / 1000).toFixed(1) + 'k'
            }
            return value.toString()
          }
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '新增用户',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#3b82f6'
          },
          lineStyle: {
            width: 3,
            color: '#3b82f6'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(59, 130, 246, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(59, 130, 246, 0.05)'
                }
              ]
            }
          },
          data: newUsers
        },
        {
          name: '活跃用户',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#10b981'
          },
          lineStyle: {
            width: 3,
            color: '#10b981'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(16, 185, 129, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(16, 185, 129, 0.05)'
                }
              ]
            }
          },
          data: activeUsers
        }
      ]
    }
    
    chart.setOption(option)
    
    // 强制调整大小
    setTimeout(() => {
      chart?.resize()
    }, 50)
    
    // 设置响应式监听
    setupResponsiveChart()
    
    loading.value = false
  } catch (error) {
    console.error('图表初始化失败:', error)
    loading.value = false
  }
}

// 设置容器可见性监听
const setupIntersectionObserver = () => {
  if (!chartRef.value || intersectionObserver) return
  
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting) {
        // 容器可见时尝试重新初始化（如果还没有图表）
        setTimeout(() => {
          if (props.data && props.data.length > 0 && !chart) {
            forceContainerSize()
            initChart()
          }
        }, 100)
      }
    },
    {
      threshold: 0.1, // 当10%的容器可见时触发
      rootMargin: '50px' // 提前50px触发
    }
  )
  
  intersectionObserver.observe(chartRef.value)
}

// 强制设置容器尺寸
const forceContainerSize = () => {
  if (chartRef.value) {
    const wrapper = chartRef.value.parentElement
    if (wrapper) {
      // 确保父容器有明确的尺寸
      wrapper.style.height = '400px'
      wrapper.style.width = '100%'
    }
    // 确保图表容器有明确的尺寸
    chartRef.value.style.height = '400px'
    chartRef.value.style.width = '100%'
  }
}

// 检查是否可以初始化图表
const tryInitChart = async () => {
  // 等待DOM更新
  await nextTick()
  
  if (chartRef.value && props.data && props.data.length > 0) {
    // 强制设置容器尺寸
    forceContainerSize()
    
    // 设置容器可见性监听
    setupIntersectionObserver()
    
    // 稍微延迟一下，确保容器尺寸已经生效
    setTimeout(() => {
      initChart()
    }, 50)
  } else {
    loading.value = true
  }
}

// 监听数据变化
watch(() => props.data, async (newData) => {
  await tryInitChart()
})

// 组件挂载后初始化
onMounted(async () => {
  await tryInitChart()
})

// 组件卸载时清理
onUnmounted(() => {
  if (chart) {
    chart._cleanup?.()
    chart.dispose()
    chart = null
  }
  cleanupObservers()
})
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 400px;
  position: relative;
  /* 确保容器有明确的尺寸 */
  display: block;
  box-sizing: border-box;
  /* 防止尺寸塌陷 */
  min-height: 400px;
  overflow: hidden;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
  /* 防止容器塌陷 */
  display: block;
  box-sizing: border-box;
  /* 确保容器始终可见以获取尺寸 */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.chart-hidden {
  opacity: 0;
  pointer-events: none;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>