<template>
  <div 
    class="user-avatar" 
    :class="[`size-${size}`, `variant-${variant}`]"
    :style="avatarStyle"
    :title="tooltip"
  >
    <span class="avatar-text">{{ displayText }}</span>
    <div v-if="variant === 'premium'" class="avatar-shine"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RecentUser } from '@/types'

interface Props {
  user?: RecentUser | null
  username?: string
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'colorful' | 'gradient' | 'premium'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  variant: 'colorful'
})

// 获取显示文本（用户名的第一个字）
const displayText = computed(() => {
  let name = ''
  
  if (props.user) {
    name = props.user.wechat_nickname || props.user.nickname || props.user.username || '?'
  } else if (props.username) {
    name = props.username
  } else {
    name = '?'
  }
  
  // 获取第一个字符（支持中文、英文）
  return name.charAt(0).toUpperCase()
})

// 工具提示
const tooltip = computed(() => {
  if (props.user) {
    const displayName = props.user.wechat_nickname || props.user.nickname || props.user.username || '未知用户'
    return `${displayName} (${props.user.email})`
  } else if (props.username) {
    return props.username
  }
  return '用户头像'
})

// 生成头像样式（根据登录方式决定颜色）
const avatarStyle = computed(() => {
  const loginType = props.user?.login_type || 'password'
  
  if (props.variant === 'default') {
    return { backgroundColor: '#e5e7eb' }
  }
  
  // 根据登录方式定义统一颜色
  const loginTypeColors = {
    'password': '#6366f1',  // 靛蓝色 - 密码登录
    'wechat': '#10b981',    // 绿色 - 微信登录  
    'phone': '#f59e0b',     // 橙色 - 手机登录
    'email': '#3b82f6',     // 蓝色 - 邮箱登录
  }
  
  const loginTypeGradients = {
    'password': 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',  // 靛蓝到紫色
    'wechat': 'linear-gradient(135deg, #10b981 0%, #34d399 100%)',    // 绿色渐变
    'phone': 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',     // 橙色渐变
    'email': 'linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%)',     // 蓝色渐变
  }
  
  if (props.variant === 'gradient' || props.variant === 'premium') {
    const gradient = loginTypeGradients[loginType as keyof typeof loginTypeGradients] || loginTypeGradients.password
    return { background: gradient }
  }
  
  // 使用登录方式对应的颜色
  const color = loginTypeColors[loginType as keyof typeof loginTypeColors] || loginTypeColors.password
  return { backgroundColor: color }
})
</script>

<style scoped>
.user-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 600;
  color: white;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

/* 尺寸变体 */
.size-small {
  width: 24px;
  height: 24px;
  font-size: 10px;
}

.size-medium {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.size-large {
  width: 48px;
  height: 48px;
  font-size: 18px;
}

/* 样式变体 */
.variant-default {
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.variant-colorful {
  /* 无阴影，适合白色背景 */
}

.variant-gradient {
  /* 无阴影，适合白色背景 */
}

.variant-premium {
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 悬停效果 */
.user-avatar:hover {
  transform: scale(1.05);
  opacity: 0.9;
}

.variant-colorful:hover {
  /* 无阴影悬停效果 */
}

.variant-gradient:hover {
  /* 无阴影悬停效果 */
}

.variant-premium:hover {
  transform: scale(1.08);
  opacity: 0.95;
}

/* 高级版闪光效果 */
.avatar-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transform: rotate(45deg);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.variant-premium:hover .avatar-shine {
  animation-duration: 0.6s;
}

/* 文字样式 */
.avatar-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  z-index: 1;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .size-medium {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .size-large {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

/* 无障碍性支持 */
.user-avatar:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 打印时隐藏动效 */
@media print {
  .user-avatar {
    transform: none !important;
    opacity: 1 !important;
  }
  
  .avatar-shine {
    display: none;
  }
}
</style> 