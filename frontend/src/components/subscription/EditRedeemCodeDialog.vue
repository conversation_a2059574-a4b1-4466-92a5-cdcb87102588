<template>
  <el-dialog
    v-model="visible"
    title="编辑兑换码"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <!-- 兑换码（只读） -->
      <el-form-item label="兑换码" prop="code">
        <el-input
          v-model="form.code"
          placeholder="兑换码"
          readonly
          disabled
        />
        <div class="text-xs text-gray-500 mt-1">兑换码不可修改</div>
      </el-form-item>

      <!-- 兑换码描述 -->
      <el-form-item label="兑换码描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入兑换码描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 最大使用次数 -->
      <el-form-item label="最大使用次数" prop="max_uses">
        <el-input-number
          v-model="form.max_uses"
          :min="form.used_count || 1"
          :max="10000"
          style="width: 200px"
        />
        <div class="text-xs text-gray-500 mt-1">
          当前已使用: {{ form.used_count || 0 }} 次
        </div>
      </el-form-item>

      <!-- 过期时间 -->
      <el-form-item label="过期时间" prop="expires_at">
        <el-date-picker
          v-model="form.expires_at"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
        <div class="text-xs text-gray-500 mt-1">留空表示永不过期</div>
      </el-form-item>

      <!-- 启用状态 -->
      <el-form-item label="启用状态">
        <el-switch 
          v-model="form.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <!-- 兑换码信息展示 -->
      <el-divider content-position="left">兑换码信息</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <div class="label">兑换类型</div>
            <div class="value">
              <el-tag v-if="form.credits" type="success">积分兑换码</el-tag>
              <el-tag v-else-if="form.package_id" type="primary">套餐兑换码</el-tag>
              <el-tag v-else type="info">未知类型</el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <div class="label">兑换内容</div>
            <div class="value">
              <span v-if="form.credits">{{ form.credits }} 积分</span>
              <span v-else-if="form.package_id">{{ form.package_id }}</span>
              <span v-else>-</span>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <div class="label">使用率</div>
            <div class="value">
              <el-progress 
                :percentage="form.usage_rate || 0" 
                :stroke-width="6"
                :show-text="false"
              />
              <span class="text-sm ml-2">{{ form.usage_rate?.toFixed(1) || 0 }}%</span>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mt-4">
        <el-col :span="12">
          <div class="info-item">
            <div class="label">创建时间</div>
            <div class="value text-sm">{{ formatDateTime(form.created_at) }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">更新时间</div>
            <div class="value text-sm">{{ formatDateTime(form.updated_at) }}</div>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useSubscriptionStore } from '@/stores/subscription'
import type { RedeemCode } from '@/types'

interface Props {
  modelValue: boolean
  redeemCode?: RedeemCode | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated', data: RedeemCode): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const subscriptionStore = useSubscriptionStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = reactive({
  id: 0,
  code: '',
  description: '',
  max_uses: 1,
  used_count: 0,
  is_active: true,
  expires_at: '',
  credits: undefined as number | undefined,
  package_id: '',
  usage_rate: 0,
  created_at: '',
  updated_at: ''
})

// 表单验证规则
const rules = computed<FormRules>(() => ({
  description: [
    { max: 200, message: '描述长度不能超过200个字符', trigger: 'blur' }
  ],
  max_uses: [
    { required: true, message: '请输入最大使用次数', trigger: 'blur' },
    { type: 'number', min: form.used_count || 1, message: `最大使用次数不能小于已使用次数(${form.used_count})`, trigger: 'blur' }
  ]
}))

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听兑换码数据变化，更新表单
watch(() => props.redeemCode, (newCode) => {
  if (newCode) {
    Object.assign(form, {
      id: newCode.id,
      code: newCode.code,
      description: newCode.description || '',
      max_uses: newCode.max_uses,
      used_count: newCode.used_count,
      is_active: newCode.is_active,
      expires_at: newCode.expires_at || '',
      credits: newCode.credits,
      package_id: newCode.package_id,
      usage_rate: newCode.usage_rate,
      created_at: newCode.created_at,
      updated_at: newCode.updated_at
    })
  }
}, { immediate: true })

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const updateData = {
      description: form.description,
      max_uses: form.max_uses,
      is_active: form.is_active,
      expires_at: form.expires_at || undefined
    }

    const result = await subscriptionStore.updateRedeemCode(form.id, updateData)
    
    ElMessage.success('兑换码更新成功')
    emit('updated', result)
    handleClose()
  } catch (error: any) {
    console.error('更新兑换码失败:', error)
    ElMessage.error(error.message || '更新兑换码失败')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.info-item {
  margin-bottom: 12px;
}

.info-item .label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.info-item .value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
