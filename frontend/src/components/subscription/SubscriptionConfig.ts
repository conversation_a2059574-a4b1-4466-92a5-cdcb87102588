// 订阅管理配置文件
export interface TableColumn {
  prop?: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean | 'custom'
  type?: 'selection' | 'index' | 'expand'
  fixed?: boolean | 'left' | 'right'
  formatter?: (row: any, column: any, cellValue: any) => string
  component?: string
}

export interface FilterConfig {
  key: string
  placeholder: string
  options: Array<{
    label: string
    value: string | number | boolean
  }>
  class?: string
}

// 支付订单表格配置
export const paymentOrderConfig = {
  searchPlaceholder: '搜索订单号、用户名或邮箱',
  showDateFilter: true,
  filters: [
    {
      key: 'status',
      placeholder: '订单状态',
      options: [
        { label: '待支付', value: 'pending' },
        { label: '已支付', value: 'paid' },
        { label: '已取消', value: 'cancelled' },
        { label: '已退款', value: 'refunded' }
      ]
    },
    {
      key: 'payment_method',
      placeholder: '支付方式',
      options: [
        { label: '微信支付', value: 'wechat' },
        { label: '支付宝', value: 'alipay' },
        { label: '银行卡', value: 'bank_card' },
        { label: '兑换码', value: 'redeem_code_free' }
      ]
    }
  ] as FilterConfig[]
}

// 兑换码表格配置
export const redeemCodeConfig = {
  searchPlaceholder: '搜索兑换码或描述',
  showDateFilter: true,
  filters: [
    {
      key: 'is_active',
      placeholder: '状态',
      options: [
        { label: '激活', value: true },
        { label: '禁用', value: false }
      ]
    },
    {
      key: 'code_type',
      placeholder: '类型',
      options: [
        { label: '积分兑换码', value: 'credits' },
        { label: '套餐兑换码', value: 'package' },
        { label: '兑换码', value: 'redeem_code_free' }
      ]
    }
  ] as FilterConfig[]
}

// 兑换记录表格配置
export const redemptionConfig = {
  searchPlaceholder: '搜索用户名或兑换码',
  showDateFilter: true,
  filters: [
    {
      key: 'status',
      placeholder: '兑换状态',
      options: [
        { label: '成功', value: 'success' },
        { label: '失败', value: 'failed' }
      ]
    }
  ] as FilterConfig[]
}

// 积分账户表格配置
export const creditAccountConfig = {
  searchPlaceholder: '搜索用户名或邮箱',
  showDateFilter: false,
  filters: [
    {
      key: 'status',
      placeholder: '账户状态',
      options: [
        { label: '正常', value: 'active' },
        { label: '冻结', value: 'frozen' }
      ]
    }
  ] as FilterConfig[]
}

// 积分交易表格配置
export const creditTransactionConfig = {
  searchPlaceholder: '搜索用户名或交易描述',
  showDateFilter: true,
  filters: [
    {
      key: 'transaction_type',
      placeholder: '交易类型',
      options: [
        { label: '充值', value: 'recharge' },
        { label: '消费', value: 'consume' },
        { label: '退款', value: 'refund' },
        { label: '兑换', value: 'redeem' },
        { label: '兑换码', value: 'redeem_code_free' }
      ]
    }
  ] as FilterConfig[]
}

// 状态映射
export const statusMaps = {
  paymentMethod: {
    wechat: { text: '微信支付', type: 'success' },
    alipay: { text: '支付宝', type: 'primary' },
    bank_card: { text: '银行卡', type: 'info' },
    redeem_code_free: { text: '兑换码', type: 'warning' }
  },
  orderStatus: {
    pending: { text: '待支付', type: 'warning' },
    paid: { text: '已支付', type: 'success' },
    cancelled: { text: '已取消', type: 'info' },
    refunded: { text: '已退款', type: 'danger' }
  },
  redeemCodeStatus: {
    'true': { text: '激活', type: 'success' },
    'false': { text: '禁用', type: 'info' }
  },
  transactionType: {
    recharge: { text: '充值', type: 'success' },
    consume: { text: '消费', type: 'warning' },
    refund: { text: '退款', type: 'primary' },
    redeem: { text: '兑换', type: 'info' },
    redeem_code_free: { text: '兑换码', type: 'warning' }
  },
  codeType: {
    credits: { text: '积分兑换码', type: 'primary' },
    package: { text: '套餐兑换码', type: 'success' },
    redeem_code_free: { text: '兑换码', type: 'info' }
  }
}

// 工具函数
export const getStatusConfig = (type: keyof typeof statusMaps, value: string | boolean) => {
  const map = statusMaps[type] as any
  const key = String(value)
  return map[key] || { text: key, type: 'info' }
} 