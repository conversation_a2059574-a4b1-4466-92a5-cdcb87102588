<template>
  <div class="data-table">
    <!-- 搜索和筛选 -->
    <div class="mb-6 flex flex-wrap gap-4 items-center justify-between">
      <div class="flex items-center gap-4">
        <el-input
          v-model="searchQuery"
          :placeholder="searchPlaceholder"
          class="w-80"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 动态筛选器 -->
        <el-select
          v-for="filter in filters"
          :key="filter.key"
          v-model="filterValues[filter.key]"
          :placeholder="filter.placeholder"
          clearable
          :class="filter.class || 'w-32'"
          @change="handleSearch"
        >
          <el-option
            v-for="option in filter.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <!-- 日期范围选择器 -->
        <el-date-picker
          v-if="showDateFilter"
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleDateChange"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-2">
        <slot name="actions" />
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="data"
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <slot name="columns" :format-currency="formatCurrency" :format-number="formatNumber" :format-date-time="formatDateTime" />
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-end mt-6">
      <el-pagination
        :current-page="pagination.page"
        :page-size="25"
        :total="pagination.total"
        layout="total, prev, pager, next"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'

interface FilterOption {
  label: string
  value: string | number | boolean
}

interface Filter {
  key: string
  placeholder: string
  options: FilterOption[]
  class?: string
}

interface Props {
  data?: any[]
  loading?: boolean
  pagination?: {
    page: number
    page_size: number
    total: number
    pages: number
  }
  searchPlaceholder?: string
  filters?: Filter[]
  showDateFilter?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  pagination: () => ({
    page: 1,
    page_size: 20,
    total: 0,
    pages: 0
  }),
  searchPlaceholder: '搜索...',
  filters: () => [],
  showDateFilter: false
})

const emit = defineEmits<{
  search: [params: any]
  refresh: []
}>()

// 搜索状态
const searchQuery = ref('')
const filterValues = reactive<Record<string, any>>({})
const dateRange = ref<[string, string] | null>(null)

// 处理搜索
const handleSearch = () => {
  const params: any = {
    search: searchQuery.value,
    ...filterValues,
    page: 1,
    page_size: 25
  }
  
  // 添加日期参数
  if (dateRange.value) {
    params.start_date = dateRange.value[0]
    params.end_date = dateRange.value[1]
  }
  
  emit('search', params)
}

// 日期范围变化
const handleDateChange = () => {
  handleSearch()
}

// 分页处理
const handlePageChange = (page: number) => {
  const params: any = {
    search: searchQuery.value,
    ...filterValues,
    page: page,
    page_size: 25
  }
  
  if (dateRange.value) {
    params.start_date = dateRange.value[0]
    params.end_date = dateRange.value[1]
  }
  
  emit('search', params)
}

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  console.log('Sort:', prop, order)
}

// 工具函数
const formatCurrency = (amount: number): string => {
  if (typeof amount !== 'number' || isNaN(amount)) return '0.00'
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatNumber = (num: number): string => {
  if (typeof num !== 'number' || isNaN(num)) return '0'
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 暴露给父组件的方法
defineExpose({
  refresh: () => emit('refresh')
})
</script>

<style scoped>
.data-table {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style> 