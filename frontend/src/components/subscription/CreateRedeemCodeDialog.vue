<template>
  <el-dialog
    v-model="visible"
    title="创建兑换码"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <!-- 兑换码类型选择 -->
      <el-form-item label="兑换码类型" prop="type">
        <el-radio-group v-model="form.type" @change="handleTypeChange">
          <el-radio value="credits">积分兑换码</el-radio>
          <el-radio value="package">套餐兑换码</el-radio>
          <el-radio value="custom_package">自定义套餐兑换码</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 积分兑换码配置 -->
      <template v-if="form.type === 'credits'">
        <el-form-item label="积分数量" prop="credits">
          <el-input-number
            v-model="form.credits"
            :min="1"
            :max="10000"
            placeholder="请输入积分数量"
            style="width: 200px"
          />
        </el-form-item>
      </template>

      <!-- 套餐兑换码配置 -->
      <template v-if="form.type === 'package'">
        <el-form-item label="选择套餐" prop="package_id">
          <el-select
            v-model="form.package_id"
            placeholder="请选择套餐"
            style="width: 300px"
            loading-text="加载套餐列表..."
          >
            <el-option
              v-for="pkg in packageOptions"
              :key="pkg.package_id"
              :label="`${pkg.name} (${pkg.package_id})`"
              :value="pkg.package_id"
            />
          </el-select>
        </el-form-item>
      </template>

      <!-- 自定义套餐配置 -->
      <template v-if="form.type === 'custom_package'">
        <el-divider content-position="left">套餐配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="套餐ID" prop="package_config.package_id">
              <el-input
                v-model="form.package_config.package_id"
                placeholder="请输入套餐ID"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="套餐名称" prop="package_config.name">
              <el-input
                v-model="form.package_config.name"
                placeholder="请输入套餐名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="套餐描述" prop="package_config.description">
          <el-input
            v-model="form.package_config.description"
            type="textarea"
            :rows="3"
            placeholder="请输入套餐描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="套餐价格" prop="package_config.amount">
              <el-input-number
                v-model="form.package_config.amount"
                :min="0"
                :precision="2"
                placeholder="价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="积分数量" prop="package_config.credits">
              <el-input-number
                v-model="form.package_config.credits"
                :min="0"
                placeholder="积分"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="赠送积分" prop="package_config.bonus">
              <el-input-number
                v-model="form.package_config.bonus"
                :min="0"
                placeholder="赠送积分"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="计费周期" prop="package_config.billing_cycle">
              <el-select v-model="form.package_config.billing_cycle" style="width: 100%">
                <el-option label="体验" value="trial" />
                <el-option label="月付" value="monthly" />
                <el-option label="年付" value="yearly" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="月均价格" prop="package_config.monthly_price">
              <el-input-number
                v-model="form.package_config.monthly_price"
                :min="0"
                :precision="2"
                placeholder="月均价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="有效期(天)" prop="package_config.validity_days">
              <el-input-number
                v-model="form.package_config.validity_days"
                :min="1"
                placeholder="有效期天数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最少用户数" prop="package_config.min_users">
              <el-input-number
                v-model="form.package_config.min_users"
                :min="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最多用户数" prop="package_config.max_users">
              <el-input-number
                v-model="form.package_config.max_users"
                :min="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序顺序" prop="package_config.sort_order">
              <el-input-number
                v-model="form.package_config.sort_order"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="体验套餐">
              <el-switch v-model="form.package_config.is_trial" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需要联系销售">
              <el-switch v-model="form.package_config.contact_required" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item 
          v-if="form.package_config.contact_required" 
          label="联系电话" 
          prop="package_config.contact_phone"
        >
          <el-input
            v-model="form.package_config.contact_phone"
            placeholder="请输入联系电话"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="套餐功能">
          <el-input
            v-model="featuresInput"
            placeholder="请输入功能特性，用逗号分隔"
            @blur="updateFeatures"
          />
          <div v-if="form.package_config.features?.length" class="mt-2">
            <el-tag
              v-for="(feature, index) in form.package_config.features"
              :key="index"
              closable
              @close="removeFeature(index)"
              class="mr-2 mb-2"
            >
              {{ feature }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="批量折扣">
          <el-switch
            v-model="enableBulkDiscount"
            @change="handleBulkDiscountToggle"
          />
          <span class="ml-2 text-sm text-gray-500">启用批量购买折扣</span>
        </el-form-item>

        <template v-if="enableBulkDiscount">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="折扣门槛" prop="bulk_discount_threshold">
                <el-input-number
                  v-model="bulkDiscountThreshold"
                  :min="2"
                  placeholder="最少购买数量"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="折扣率" prop="bulk_discount_rate">
                <el-input-number
                  v-model="bulkDiscountRate"
                  :min="0.1"
                  :max="1"
                  :step="0.01"
                  :precision="2"
                  placeholder="0.9 表示 9 折"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="折后价格" prop="bulk_discount_amount">
                <el-input-number
                  v-model="bulkDiscountAmount"
                  :min="0"
                  :precision="2"
                  placeholder="折后价格"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </template>

      <el-divider content-position="left">兑换码配置</el-divider>

      <!-- 兑换码基本信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="兑换码" prop="code">
            <el-input
              v-model="form.code"
              placeholder="请输入兑换码"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大使用次数" prop="max_uses">
            <el-input-number
              v-model="form.max_uses"
              :min="1"
              :max="10000"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="兑换码描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="2"
          placeholder="请输入兑换码描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="过期时间" prop="expires_at">
        <el-date-picker
          v-model="form.expires_at"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px"
        />
      </el-form-item>

      <el-form-item label="启用状态">
        <el-switch v-model="form.is_active" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          创建兑换码
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useSubscriptionStore } from '@/stores/subscription'
import type { RedeemCodeCreate } from '@/types'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const subscriptionStore = useSubscriptionStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const featuresInput = ref('')
const packageOptions = ref<any[]>([])

// 批量折扣相关变量
const enableBulkDiscount = ref(false)
const bulkDiscountThreshold = ref<number | undefined>(undefined)
const bulkDiscountRate = ref<number | undefined>(undefined)
const bulkDiscountAmount = ref<number | undefined>(undefined)

// 表单数据
const form = reactive({
  type: 'credits' as 'credits' | 'package' | 'custom_package',
  code: '',
  credits: undefined as number | undefined,
  package_id: '',
  description: '',
  max_uses: 1,
  expires_at: '',
  is_active: true,
  // 自定义套餐配置
  package_config: {
    package_id: '',
    name: '',
    description: '',
    amount: 0,
    credits: 0,
    bonus: 0,
    billing_cycle: 'monthly' as 'trial' | 'monthly' | 'yearly',
    monthly_price: undefined as number | undefined,
    min_users: 1,
    max_users: 1,
    validity_days: undefined as number | undefined,
    is_trial: false,
    contact_required: false,
    contact_phone: '',
    features: [] as string[],
    bulk_discount: null as any,
    is_active: true,
    sort_order: 0
  }
})

// 表单验证规则
const rules = computed<FormRules>(() => {
  const baseRules: FormRules = {
    type: [{ required: true, message: '请选择兑换码类型', trigger: 'change' }],
    code: [
      { required: true, message: '请输入兑换码', trigger: 'blur' },
      { min: 3, max: 50, message: '兑换码长度应在 3-50 个字符', trigger: 'blur' }
    ],
    max_uses: [{ required: true, message: '请输入最大使用次数', trigger: 'blur' }]
  }

  if (form.type === 'credits') {
    baseRules.credits = [{ required: true, message: '请输入积分数量', trigger: 'blur' }]
  }

  if (form.type === 'package') {
    baseRules.package_id = [{ required: true, message: '请选择套餐', trigger: 'change' }]
  }

  if (form.type === 'custom_package') {
    baseRules['package_config.package_id'] = [
      { required: true, message: '请输入套餐ID', trigger: 'blur' }
    ]
    baseRules['package_config.name'] = [
      { required: true, message: '请输入套餐名称', trigger: 'blur' }
    ]
    baseRules['package_config.amount'] = [
      { required: true, message: '请输入套餐价格', trigger: 'blur' }
    ]
    baseRules['package_config.credits'] = [
      { required: true, message: '请输入积分数量', trigger: 'blur' }
    ]
    baseRules['package_config.billing_cycle'] = [
      { required: true, message: '请选择计费周期', trigger: 'change' }
    ]

    if (form.package_config.contact_required) {
      baseRules['package_config.contact_phone'] = [
        { required: true, message: '请输入联系电话', trigger: 'blur' }
      ]
    }
  }

  return baseRules
})

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听类型变化，重置相关字段
const handleTypeChange = () => {
  form.credits = undefined
  form.package_id = ''
  resetPackageConfig()
}

// 重置套餐配置
const resetPackageConfig = () => {
  Object.assign(form.package_config, {
    package_id: '',
    name: '',
    description: '',
    amount: 0,
    credits: 0,
    bonus: 0,
    billing_cycle: 'monthly',
    monthly_price: undefined,
    min_users: 1,
    max_users: 1,
    validity_days: undefined,
    is_trial: false,
    contact_required: false,
    contact_phone: '',
    features: [],
    bulk_discount: null,
    is_active: true,
    sort_order: 0
  })
  featuresInput.value = ''

  // 重置批量折扣相关变量
  enableBulkDiscount.value = false
  bulkDiscountThreshold.value = undefined
  bulkDiscountRate.value = undefined
  bulkDiscountAmount.value = undefined
}

// 更新功能特性
const updateFeatures = () => {
  if (featuresInput.value.trim()) {
    form.package_config.features = featuresInput.value
      .split(',')
      .map(f => f.trim())
      .filter(f => f.length > 0)
  }
}

// 移除功能特性
const removeFeature = (index: number) => {
  form.package_config.features.splice(index, 1)
  featuresInput.value = form.package_config.features.join(', ')
}

// 处理批量折扣开关
const handleBulkDiscountToggle = () => {
  if (enableBulkDiscount.value) {
    // 启用批量折扣时，设置默认值
    bulkDiscountThreshold.value = 5
    bulkDiscountRate.value = 0.9
    updateBulkDiscount()
  } else {
    // 禁用批量折扣时，清空相关数据
    bulkDiscountThreshold.value = undefined
    bulkDiscountRate.value = undefined
    bulkDiscountAmount.value = undefined
    form.package_config.bulk_discount = null
  }
}

// 更新批量折扣配置
const updateBulkDiscount = () => {
  if (enableBulkDiscount.value && bulkDiscountThreshold.value && bulkDiscountRate.value) {
    // 计算折后价格
    const originalAmount = form.package_config.amount || 0
    const discountedAmount = originalAmount * bulkDiscountRate.value
    bulkDiscountAmount.value = Math.round(discountedAmount * 100) / 100

    // 计算折后月均价格
    const originalMonthlyPrice = form.package_config.monthly_price || 0
    const discountedMonthlyPrice = originalMonthlyPrice * bulkDiscountRate.value

    // 更新 bulk_discount 对象
    form.package_config.bulk_discount = {
      threshold: bulkDiscountThreshold.value,
      discount_rate: bulkDiscountRate.value,
      discounted_amount: bulkDiscountAmount.value,
      discounted_monthly_price: Math.round(discountedMonthlyPrice * 100) / 100
    }
  } else {
    form.package_config.bulk_discount = null
  }
}

// 监听价格变化，自动更新折扣价格
watch([() => form.package_config.amount, () => form.package_config.monthly_price], () => {
  if (enableBulkDiscount.value) {
    updateBulkDiscount()
  }
})

// 监听折扣参数变化
watch([bulkDiscountThreshold, bulkDiscountRate], () => {
  if (enableBulkDiscount.value) {
    updateBulkDiscount()
  }
})

// 加载套餐选项
const loadPackageOptions = async () => {
  try {
    // 这里应该调用获取套餐配置的API
    // const response = await subscriptionStore.fetchPackageConfigs()
    // packageOptions.value = response.items || []

    // 临时使用模拟数据
    packageOptions.value = [
      { package_id: 'trial_package', name: '体验套餐' },
      { package_id: 'personal_standard', name: '个人标准版' },
      { package_id: 'personal_professional', name: '个人专业版' },
      { package_id: 'business_flagship', name: '商业旗舰版' },
      { package_id: 'enterprise_flagship', name: '企业旗舰版' }
    ]
  } catch (error) {
    console.error('加载套餐选项失败:', error)
    ElMessage.error('加载套餐选项失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    let result
    if (form.type === 'custom_package') {
      // 创建自定义套餐和兑换码
      const requestData = {
        package_config: { ...form.package_config },
        redeem_code: form.code,
        redeem_description: form.description,
        max_uses: form.max_uses,
        expires_at: form.expires_at || undefined
      }

      result = await subscriptionStore.createCustomPackageRedeemCode(requestData)
    } else {
      // 创建普通兑换码
      const redeemCodeData: RedeemCodeCreate = {
        code: form.code,
        description: form.description,
        max_uses: form.max_uses,
        is_active: form.is_active,
        expires_at: form.expires_at || undefined
      }

      if (form.type === 'credits') {
        redeemCodeData.credits = form.credits
      } else if (form.type === 'package') {
        redeemCodeData.package_id = form.package_id
      }

      result = await subscriptionStore.createRedeemCode(redeemCodeData)
    }

    ElMessage.success('兑换码创建成功')
    emit('created', result)
    handleClose()
  } catch (error: any) {
    console.error('创建兑换码失败:', error)
    ElMessage.error(error.message || '创建兑换码失败')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields()
  resetPackageConfig()
  featuresInput.value = ''
  visible.value = false
}

// 组件挂载时加载套餐选项
onMounted(() => {
  loadPackageOptions()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
