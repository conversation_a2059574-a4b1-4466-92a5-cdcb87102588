<template>
  <div class="stats-card">
    <div class="stats-header">
      <div class="stats-icon-wrapper" :style="{ backgroundColor: `${color}15` }">
        <el-icon :size="20" :color="color">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div v-if="growth !== undefined" class="stats-trend">
        <el-icon :size="16" :class="growthClass">
          <component :is="growth >= 0 ? 'TrendCharts' : 'Bottom'" />
        </el-icon>
        <span :class="growthClass" class="growth-text">{{ formatGrowth(growth) }}</span>
      </div>
    </div>
    
    <div class="stats-body">
      <div class="stats-value-section">
        <span class="stats-value">{{ formatValue(value) }}</span>
        <span v-if="suffix" class="stats-suffix">{{ suffix }}</span>
      </div>
      <h3 class="stats-title">{{ title }}</h3>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  ShoppingCart, Clock, Money, Ticket, CreditCard, 
  Check, Star, TrendCharts, Bottom 
} from '@element-plus/icons-vue'

interface Props {
  title: string
  value: number
  icon: string
  color: string
  suffix?: string
  growth?: number
}

const props = withDefaults(defineProps<Props>(), {
  suffix: '',
  growth: undefined
})

// 图标映射
const iconMap = {
  ShoppingCart,
  Clock,
  Money,
  Ticket,
  CreditCard,
  Check,
  Star,
  TrendCharts,
  Bottom
}

// 动态获取图标组件
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || ShoppingCart
})

// 格式化数值
const formatValue = (value: number): string => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + 'w'
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  }
  return value.toLocaleString()
}

// 格式化增长率
const formatGrowth = (growth: number): string => {
  const sign = growth >= 0 ? '+' : ''
  return `${sign}${growth.toFixed(1)}%`
}

// 增长率样式类
const growthClass = computed(() => {
  if (props.growth === undefined) return ''
  return props.growth >= 0 ? 'trend-up' : 'trend-down'
})
</script>

<style scoped>
.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color, #3b82f6), var(--secondary-color, #8b5cf6));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: rgba(226, 232, 240, 0.6);
}

.stats-card:hover::before {
  opacity: 1;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.stats-card:hover .stats-icon-wrapper {
  transform: scale(1.1);
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

.trend-up {
  color: #059669;
}

.trend-down {
  color: #dc2626;
}

.growth-text {
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.stats-body {
  space-y: 8px;
}

.stats-value-section {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 36px;
  font-weight: 800;
  color: #0f172a;
  line-height: 1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #0f172a 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-suffix {
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  opacity: 0.8;
}

.stats-title {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  margin: 0;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-card {
    padding: 20px;
  }
  
  .stats-value {
    font-size: 32px;
  }
  
  .stats-icon-wrapper {
    width: 44px;
    height: 44px;
  }
  
  .stats-header {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .stats-card {
    padding: 16px;
  }
  
  .stats-value {
    font-size: 28px;
  }
  
  .stats-icon-wrapper {
    width: 40px;
    height: 40px;
  }
  
  .stats-title {
    font-size: 13px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .stats-card {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: rgba(71, 85, 105, 0.3);
  }
  
  .stats-value {
    background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .stats-title {
    color: #94a3b8;
  }
  
  .stats-suffix {
    color: #94a3b8;
  }
  
  .stats-trend {
    background: rgba(30, 41, 59, 0.8);
  }
}
</style> 