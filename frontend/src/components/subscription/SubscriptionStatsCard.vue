<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- 总订单数 -->
    <StatsCard
      title="总订单数"
      :value="Number(stats?.total_orders) || 0"
      icon="ShoppingCart"
      color="#3b82f6"
      :growth="orderGrowth"
    />

    <!-- 待支付订单 -->
    <StatsCard
      title="待支付订单"
      :value="Number(stats?.pending_orders) || 0"
      icon="Clock"
      color="#f59e0b"
      :growth="pendingGrowth"
    />

    <!-- 总收入 -->
    <StatsCard
      title="总收入"
      :value="Number(stats?.total_revenue) || 0"
      icon="Money"
      color="#10b981"
      suffix="¥"
      :growth="revenueGrowth"
    />

    <!-- 活跃兑换码 -->
    <StatsCard
      title="活跃兑换码"
      :value="Number(stats?.active_redeem_codes) || 0"
      icon="Ticket"
      color="#8b5cf6"
      :growth="codeGrowth"
    />

    <!-- 总销售积分 -->
    <StatsCard
      title="总销售积分"
      :value="Number(stats?.total_credits_sold) || 0"
      icon="CreditCard"
      color="#6366f1"
      :growth="creditsGrowth"
    />

    <!-- 已支付订单 -->
    <StatsCard
      title="已支付订单"
      :value="Number(stats?.paid_orders) || 0"
      icon="Check"
      color="#059669"
      :growth="paidGrowth"
    />

    <!-- 总兑换次数 -->
    <StatsCard
      title="总兑换次数"
      :value="Number(stats?.total_redemptions) || 0"
      icon="Star"
      color="#0891b2"
      :growth="redemptionGrowth"
    />

    <!-- 支付成功率 -->
    <StatsCard
      title="支付成功率"
      :value="successRate"
      icon="TrendCharts"
      color="#059669"
      suffix="%"
      :growth="successRateGrowth"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import StatsCard from './StatsCard.vue'
import type { SubscriptionStats } from '@/types'

interface Props {
  stats: SubscriptionStats | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 计算支付成功率
const successRate = computed(() => {
  if (!props.stats || props.stats.total_orders === 0) return 0
  return Math.round((props.stats.paid_orders / props.stats.total_orders) * 100)
})

// 使用从API获取的真实增长率数据
const orderGrowth = computed(() => props.stats?.orders_growth_rate || 0)
const revenueGrowth = computed(() => props.stats?.revenue_growth_rate || 0)
const creditsGrowth = computed(() => props.stats?.credits_growth_rate || 0)
const redemptionGrowth = computed(() => props.stats?.redemptions_growth_rate || 0)

// 对于没有增长率数据的指标，使用undefined表示不显示增长率
const pendingGrowth = computed(() => undefined) // 待支付订单不显示增长率
const codeGrowth = computed(() => undefined) // 活跃兑换码不显示增长率
const paidGrowth = computed(() => undefined) // 已支付订单使用总订单增长率
const successRateGrowth = computed(() => undefined) // 支付成功率不显示增长率
</script>

 