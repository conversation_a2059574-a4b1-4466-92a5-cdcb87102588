<template>
  <div class="user-table-container">
    <!-- 筛选和搜索栏 -->
    <div class="table-controls">
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名、邮箱、昵称或微信昵称"
          prefix-icon="Search"
          clearable
          class="search-input"
          @input="handleSearch"
          @clear="handleSearchClear"
        />
      </div>
      
      <div class="filter-section">
        <el-select
          v-model="localFilters.role"
          placeholder="角色筛选"
          clearable
          class="filter-select"
          @change="handleFilterChange"
        >
          <el-option label="管理员" value="admin" />
          <el-option label="普通用户" value="user" />
        </el-select>
        
        <el-select
          v-model="localFilters.is_active"
          placeholder="状态筛选"
          clearable
          class="filter-select"
          @change="handleFilterChange"
        >
          <el-option label="活跃" :value="true" />
          <el-option label="非活跃" :value="false" />
        </el-select>
        
        <el-select
          v-model="localFilters.login_type"
          placeholder="登录方式"
          clearable
          class="filter-select"
          @change="handleFilterChange"
        >
          <el-option label="密码登录" value="password" />
          <el-option label="微信登录" value="wechat" />
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="注册开始时间"
          end-placeholder="注册结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="date-picker"
          @change="handleDateRangeChange"
        />
        
        <el-button
          type="default"
          @click="handleResetFilters"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        
        <el-button
          type="primary"
          @click="handleRefresh"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-wrapper">
      <el-table
        :data="users"
        v-loading="loading"
        stripe
        class="user-table"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        
        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <div class="user-avatar">
                                 <UserAvatar 
                   :user="row" 
                   size="large"
                 />
              </div>
              <div class="user-details">
                <div class="username">{{ getDisplayName(row) }}</div>
                <div class="email">{{ row.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="role" label="角色" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.role === 'admin' ? 'danger' : 'primary'" 
              size="small"
            >
              {{ row.role === 'admin' ? '管理员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="is_active" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.is_active ? 'success' : 'warning'" 
              size="small"
            >
              {{ row.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="login_type" label="登录方式" width="120" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.login_type === 'wechat' ? 'success' : 'info'" 
              size="small"
            >
              <el-icon class="mr-1">
                <component :is="row.login_type === 'wechat' ? 'ChatDotRound' : 'Lock'" />
              </el-icon>
              {{ row.login_type === 'wechat' ? '微信' : '密码' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_login" label="最后登录" width="160" align="center" sortable="custom">
          <template #default="{ row }">
            <div v-if="row.last_login" class="text-sm">
              {{ formatDateTime(row.last_login) }}
            </div>
            <span v-else class="text-gray-400">从未登录</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="注册时间" width="160" align="center" sortable="custom">
          <template #default="{ row }">
            <div class="text-sm">{{ formatDateTime(row.created_at) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleViewDetail(row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper flex justify-end">
      <el-pagination
        v-model:current-page="pagination.page"
        :page-size="25"
        :total="pagination.total"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户详情对话框 -->
    <UserDetailDialog
      v-model="showDetailDialog"
      :user-id="selectedUserId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UserAvatar from '@/components/common/UserAvatar.vue'
import UserDetailDialog from '@/components/user-management/UserDetailDialog.vue'
import { formatDateTime } from '@/utils/date'
import type { UserDetail, UserFilters } from '@/types'

interface Props {
  users: UserDetail[]
  loading?: boolean
  pagination: {
    page: number
    page_size: number
    total: number
    pages: number
  }
  filters: UserFilters
}

interface Emits {
  (e: 'search', query: string): void
  (e: 'filter-change', filters: Partial<UserFilters>): void
  (e: 'page-change', page: number): void
  (e: 'sort-change', prop: string, order: string): void
  (e: 'refresh'): void
  (e: 'reset-filters'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 本地状态
const searchQuery = ref('')
const localFilters = reactive<UserFilters>({ ...props.filters })
const dateRange = ref<[string, string] | null>(null)
const showDetailDialog = ref(false)
const selectedUserId = ref<number | null>(null)

// 获取显示名称
const getDisplayName = (user: UserDetail): string => {
  if (user.wechat_nickname) {
    return user.wechat_nickname
  }
  if (user.nickname) {
    return user.nickname
  }
  return user.username
}

// 处理搜索
const handleSearch = () => {
  emit('search', searchQuery.value)
}

// 处理搜索清除
const handleSearchClear = () => {
  searchQuery.value = ''
  emit('search', '')
}

// 处理筛选变化
const handleFilterChange = () => {
  const filters: Partial<UserFilters> = {}
  
  if (localFilters.role) filters.role = localFilters.role
  if (localFilters.is_active !== undefined) filters.is_active = localFilters.is_active
  if (localFilters.login_type) filters.login_type = localFilters.login_type
  
  emit('filter-change', filters)
}

// 处理日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
  const filters: Partial<UserFilters> = {}
  
  if (dates && dates.length === 2) {
    filters.start_date = dates[0]
    filters.end_date = dates[1]
  } else {
    filters.start_date = undefined
    filters.end_date = undefined
  }
  
  emit('filter-change', filters)
}

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc'
    emit('sort-change', prop, sortOrder)
  }
}

// 处理页面变化
const handleCurrentChange = (page: number) => {
  emit('page-change', page)
}

// 页面大小固定为25，不需要处理页面大小变化

// 处理重置筛选
const handleResetFilters = () => {
  searchQuery.value = ''
  Object.assign(localFilters, {
    role: undefined,
    is_active: undefined,
    login_type: undefined
  })
  dateRange.value = null
  emit('reset-filters')
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 处理查看详情
const handleViewDetail = (user: UserDetail) => {
  selectedUserId.value = user.id
  showDetailDialog.value = true
}
</script>

<style scoped>
.user-table-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.table-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.search-section {
  display: flex;
  gap: 12px;
}

.search-input {
  width: 400px;
}

.filter-section {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-select {
  width: 140px;
}

.date-picker {
  width: 300px;
}

.table-wrapper {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.user-table {
  width: 100%;
}

:deep(.el-table__header) {
  background-color: #f8fafc;
}

:deep(.el-table__row:hover > td) {
  background-color: #f1f5f9 !important;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  flex: 1;
}

.username {
  font-weight: 600;
  color: #374151;
  margin-bottom: 2px;
}

.email {
  font-size: 12px;
  color: #6b7280;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .search-input {
    width: 300px;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-select,
  .date-picker {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .user-table-container {
    padding: 16px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .table-controls {
    gap: 12px;
  }
}
</style> 