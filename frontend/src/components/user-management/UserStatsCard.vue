<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6">
    <!-- 总用户数 -->
    <StatsCard
      title="总用户数"
      :value="Number(stats?.total_users) || 0"
      icon="UserFilled"
      color="#3b82f6"
      suffix="人"
    />

    <!-- 活跃用户 -->
    <StatsCard
      title="活跃用户"
      :value="Number(stats?.active_users) || 0"
      icon="User"
      color="#10b981"
      suffix="人"
    />

    <!-- 今日注册 -->
    <StatsCard
      title="今日注册"
      :value="Number(stats?.today_registered) || 0"
      icon="UserFilled"
      color="#f59e0b"
      suffix="人"
    />

    <!-- 本周注册 -->
    <StatsCard
      title="本周注册"
      :value="Number(stats?.this_week_registered) || 0"
      icon="Calendar"
      color="#8b5cf6"
      suffix="人"
    />

    <!-- 本月注册 -->
    <StatsCard
      title="本月注册"
      :value="Number(stats?.this_month_registered) || 0"
      icon="DataAnalysis"
      color="#06b6d4"
      suffix="人"
    />

    <!-- 管理员用户 -->
    <StatsCard
      title="管理员用户"
      :value="Number(stats?.admin_users) || 0"
      icon="Key"
      color="#ef4444"
      suffix="人"
    />

    <!-- 微信用户 -->
    <StatsCard
      title="微信登录"
      :value="Number(stats?.wechat_users) || 0"
      icon="ChatDotRound"
      color="#07c160"
      suffix="人"
    />

    <!-- 密码用户 -->
    <StatsCard
      title="密码登录"
      :value="Number(stats?.password_users) || 0"
      icon="Lock"
      color="#64748b"
      suffix="人"
    />

    <!-- 非活跃用户 -->
    <StatsCard
      title="非活跃用户"
      :value="Number(stats?.inactive_users) || 0"
      icon="Warning"
      color="#f97316"
      suffix="人"
    />

    <!-- 普通用户 -->
    <StatsCard
      title="普通用户"
      :value="Number(stats?.regular_users) || 0"
      icon="Avatar"
      color="#6366f1"
      suffix="人"
    />
  </div>
</template>

<script setup lang="ts">
import StatsCard from '@/components/dashboard/StatsCard.vue'
import type { UserStats } from '@/types'

interface Props {
  stats: UserStats | null
  loading?: boolean
}

withDefaults(defineProps<Props>(), {
  loading: false
})
</script> 