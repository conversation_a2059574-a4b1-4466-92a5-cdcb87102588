<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户详情"
    width="600px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="user-detail-content">
      <div v-if="userDetail" class="user-detail">
        <!-- 用户基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>用户ID</label>
              <span>{{ userDetail.id }}</span>
            </div>
            <div class="detail-item">
              <label>用户名</label>
              <span>{{ userDetail.username }}</span>
            </div>
            <div class="detail-item">
              <label>邮箱</label>
              <span>{{ userDetail.email }}</span>
            </div>
            <div class="detail-item">
              <label>昵称</label>
              <span>{{ userDetail.nickname || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label>微信昵称</label>
              <span>{{ userDetail.wechat_nickname || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label>角色</label>
              <el-tag :type="userDetail.role === 'admin' ? 'danger' : 'primary'" size="small">
                {{ userDetail.role === 'admin' ? '管理员' : '普通用户' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>状态</label>
              <el-tag :type="userDetail.is_active ? 'success' : 'warning'" size="small">
                {{ userDetail.is_active ? '活跃' : '非活跃' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>登录方式</label>
              <el-tag :type="userDetail.login_type === 'wechat' ? 'success' : 'info'" size="small">
                <el-icon class="mr-1">
                  <component :is="userDetail.login_type === 'wechat' ? 'ChatDotRound' : 'Lock'" />
                </el-icon>
                {{ userDetail.login_type === 'wechat' ? '微信登录' : '密码登录' }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 微信信息 -->
        <div v-if="userDetail.login_type === 'wechat'" class="detail-section">
          <h3 class="section-title">微信信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>OpenID</label>
              <span class="text-mono">{{ userDetail.openid || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label>UnionID</label>
              <span class="text-mono">{{ userDetail.unionid || '未设置' }}</span>
            </div>
            <div class="detail-item">
              <label>头像</label>
              <div v-if="userDetail.avatar_url" class="avatar-preview">
                <el-image
                  :src="userDetail.avatar_url"
                  fit="cover"
                  class="user-avatar-large"
                />
              </div>
              <span v-else>未设置</span>
            </div>
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h3 class="section-title">时间信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <label>注册时间</label>
              <span>{{ formatDateTime(userDetail.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>最后更新</label>
              <span>{{ formatDateTime(userDetail.updated_at) }}</span>
            </div>
            <div class="detail-item">
              <label>最后登录</label>
              <span>{{ userDetail.last_login ? formatDateTime(userDetail.last_login) : '从未登录' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserManagementStore } from '@/stores/user-management'
import { formatDateTime } from '@/utils/date'
import type { UserDetail } from '@/types'

interface Props {
  modelValue: boolean
  userId: number | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const userManagementStore = useUserManagementStore()

// 状态
const dialogVisible = ref(false)
const loading = ref(false)
const userDetail = ref<UserDetail | null>(null)

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.userId) {
    fetchUserDetail()
  }
})

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  if (!newValue) {
    emit('update:modelValue', false)
    userDetail.value = null
  }
})

// 获取用户详情
const fetchUserDetail = async () => {
  if (!props.userId) return
  
  try {
    loading.value = true
    userDetail.value = await userManagementStore.fetchUserDetail(props.userId)
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
    handleClose()
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.user-detail-content {
  min-height: 200px;
}

.user-detail {
  padding: 8px 0;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  font-size: 14px;
  color: #374151;
  word-break: break-all;
}

.text-mono {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.avatar-preview {
  display: flex;
  align-items: center;
}

.user-avatar-large {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 2px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style> 