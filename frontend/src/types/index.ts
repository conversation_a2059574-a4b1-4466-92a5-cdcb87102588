// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  role: string
  is_active: boolean
  avatar_url?: string
  login_type: string
  last_login?: string
  created_at?: string
}

export interface LoginForm {
  username: string
  password: string
}

export interface AuthToken {
  access_token: string
  token_type: string
  expires_in: number
}

// Dashboard相关类型
export interface DashboardStats {
  total_users: number
  today_new_users: number
  today_growth_rate: number
  yesterday_active_users: number
  week_active_users: number
  month_active_users: number
}

export interface UserActivity {
  date: string
  new_users: number
  active_users: number
}

export interface RecentUser {
  id: number
  username: string
  nickname?: string
  wechat_nickname?: string
  email: string
  login_type: string
  last_login?: string
  created_at?: string
}

// 用户管理相关类型
export interface UserDetail {
  id: number
  username: string
  email: string
  nickname?: string
  wechat_nickname?: string
  role: string
  is_active: boolean
  avatar_url?: string
  login_type: string
  openid?: string
  unionid?: string
  last_login?: string
  created_at?: string
  updated_at?: string
}

export interface UserStats {
  total_users: number
  active_users: number
  inactive_users: number
  admin_users: number
  regular_users: number
  wechat_users: number
  password_users: number
  today_registered: number
  this_week_registered: number
  this_month_registered: number
}

export interface UserFilters {
  search?: string
  role?: string
  is_active?: boolean
  login_type?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  start_date?: string
  end_date?: string
}

// 订阅管理相关类型
export interface PaymentOrder {
  id: number
  order_no: string
  user_id: number
  user_credit_account_id: number
  amount: number
  credits: number
  payment_method: string
  package_id?: string
  status: string
  trade_no?: string
  payment_time?: string
  refund_amount: number
  refund_reason?: string
  refund_time?: string
  remark?: string
  expires_at: string
  created_at?: string
  updated_at?: string
  user_username?: string
  user_email?: string
  user_wechat_nickname?: string
  user_nickname?: string
}

export interface RedeemCode {
  id: number
  code: string
  credits?: number
  package_id?: string
  description?: string
  max_uses: number
  used_count: number
  is_active: boolean
  expires_at?: string
  created_by?: number
  created_at: string
  updated_at: string
  is_expired: boolean
  usage_rate: number
}

export interface RedeemCodeCreate {
  code: string
  credits?: number
  package_id?: string
  description?: string
  max_uses: number
  is_active: boolean
  expires_at?: string
}

export interface RedeemCodeRedemption {
  id: number
  redeem_code_id: number
  user_id: number
  credits_received?: number
  package_order_id?: number
  transaction_id?: number
  redeemed_at: string
  redeem_code?: string
  user_username?: string
  user_email?: string
  user_wechat_nickname?: string
  user_nickname?: string
}

export interface PackageConfig {
  id: number
  package_id: string
  name: string
  description?: string
  amount: number
  credits: number
  bonus: number
  billing_cycle: 'trial' | 'monthly' | 'yearly'
  monthly_price?: number
  min_users: number
  max_users: number
  validity_days?: number
  is_trial: boolean
  contact_required: boolean
  contact_phone?: string
  features?: string[]
  bulk_discount?: any
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface PackageConfigCreate {
  package_id: string
  name: string
  description?: string
  amount: number
  credits: number
  bonus: number
  billing_cycle: 'trial' | 'monthly' | 'yearly'
  monthly_price?: number
  min_users: number
  max_users: number
  validity_days?: number
  is_trial: boolean
  contact_required: boolean
  contact_phone?: string
  features?: string[]
  bulk_discount?: any
  is_active: boolean
  sort_order: number
}

export interface UserCreditAccount {
  id: number
  user_id: number
  credit_balance: number
  total_credits_purchased: number
  total_credits_consumed: number
  is_active: boolean
  last_recharge_at?: string
  last_consumption_at?: string
  created_at?: string
  updated_at?: string
  user_username?: string
  user_email?: string
}

export interface CreditTransaction {
  id: number
  user_id: number
  user_credit_account_id: number
  transaction_type: string
  amount: number
  balance_before: number
  balance_after: number
  order_id?: number
  service_type?: string
  operation_type?: string
  request_id?: string
  tokens_consumed?: number
  conversion_rate: number
  description?: string
  created_at?: string
  user_username?: string
  order_no?: string
}

export interface SubscriptionStats {
  total_orders: number
  pending_orders: number
  paid_orders: number
  total_revenue: number
  total_credits_sold: number
  active_redeem_codes: number
  total_redemptions: number
  
  // 增长率数据
  orders_growth_rate?: number
  revenue_growth_rate?: number
  credits_growth_rate?: number
  redemptions_growth_rate?: number
}

export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  page_size: number
  pages: number
}

// API响应类型
export interface ApiResponse<T = any> {
  data?: T
  message?: string
  code?: number
}