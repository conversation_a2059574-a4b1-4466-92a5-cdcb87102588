import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/views/LayoutView.vue'),
      redirect: '/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/DashboardView.vue'),
          meta: { title: '仪表盘' }
        },
        {
          path: '/subscription',
          name: 'Subscription',
          component: () => import('@/views/SubscriptionView.vue'),
          meta: { title: '订阅管理' }
        },
        {
          path: '/user-management',
          name: 'UserManagement',
          component: () => import('@/views/UserManagementView.vue'),
          meta: { title: '用户管理' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/dashboard'
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查路由是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      next({ name: 'Login' })
      return
    }
    
    // 如果有token但没有用户信息，尝试获取
    if (!authStore.user) {
      try {
        await authStore.getCurrentUser()
      } catch {
        next({ name: 'Login' })
        return
      }
    }
    
    // 检查管理员权限
    if (!authStore.isAdmin) {
      // 可以跳转到无权限页面或者直接拒绝
      next({ name: 'Login' })
      return
    }
  }
  
  // 如果已登录且访问登录页，重定向到仪表盘
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next({ name: 'Dashboard' })
    return
  }
  
  next()
})

export default router
