/* 基础CSS重置和全局样式 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #1f2937;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移除默认列表样式 */
ul, ol {
  list-style: none;
}

/* 图片响应式 */
img {
  max-width: 100%;
  height: auto;
}

/* 表单元素基础样式 */
input, button, textarea, select {
  font: inherit;
}

/* 按钮基础样式 */
button {
  cursor: pointer;
  border: none;
  background: none;
}

/* 链接基础样式 */
a {
  color: inherit;
  text-decoration: none;
}

/* 表格基础样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 文本选择样式 */
::selection {
  background-color: #3b82f6;
  color: white;
}

/* 焦点轮廓 */
:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
