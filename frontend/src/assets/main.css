@import "tailwindcss";
@import './base.css';
@import '../styles/design-system.css';
@import '../styles/element-plus-overrides.css';

/* 全局应用样式 */
body {
  font-family: var(--font-family);
  line-height: var(--leading-normal);
  color: var(--text-body);
  background-color: var(--bg-body);
  transition: background-color var(--duration-normal) var(--ease-out);
}

/* 链接样式 */
a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color var(--duration-normal) var(--ease-out);
}

a:hover {
  color: var(--primary-indigo);
  text-decoration: underline;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-dark);
  border-radius: var(--radius-sm);
  transition: background var(--duration-fast) var(--ease-out);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-disabled);
}

/* 焦点可见性 */
.focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}
