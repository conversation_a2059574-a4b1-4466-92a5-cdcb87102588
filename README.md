# 🏢 TunshuEdu BMS - 囤鼠教育后台管理系统

[![Vue.js](https://img.shields.io/badge/Vue.js-3.4-4FC08D?style=flat&logo=vue.js&logoColor=white)](https://vuejs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104-009688?style=flat&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=flat&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.4-409eff?style=flat&logo=element&logoColor=white)](https://element-plus.org/)

## 🎯 项目概述

TunshuEdu BMS（Backend Management System）是囤鼠教育的专业后台管理系统，为管理员提供用户数据分析、系统监控和管理功能。采用现代化技术栈构建，提供简约高级的管理界面。

## ✨ 核心特性

### 🔐 **管理员认证系统**
- JWT令牌认证机制
- 管理员权限验证
- 安全的登录/登出流程
- 自动令牌刷新

### 📊 **三层Dashboard架构**

#### **第一层：核心运营指标卡片**
- 📈 总注册用户数（累计统计）
- 🆕 今日新增用户数（含同比增长率）
- ⚡ 昨日活跃用户数（DAU）
- 📅 近7日活跃用户数（WAU）
- 📆 近30日活跃用户数（MAU）

#### **第二层：数据分析图表**
- 📊 用户增长趋势图（ECharts图表）
- 🔄 支持7天/30天/90天维度切换
- 📈 新增用户vs活跃用户对比分析
- 🎨 精美的渐变面积图设计

#### **第三层：详细数据管理**
- 👥 最近用户列表（支持分页）
- 🔍 用户搜索功能（用户名/邮箱）
- 🏷️ 登录方式标签（微信/密码等）
- ⏰ 智能时间显示（今天/昨天/N天前）
- 🛠️ 用户操作菜单（查看/编辑/禁用）

### 🎨 **现代化UI设计**
- 📱 完全响应式设计
- 🌈 精美的渐变色彩方案
- 💫 流畅的动画效果
- 🖼️ 卡片式布局设计
- 🔀 侧边栏折叠功能

## 🛠️ 技术架构

### 前端技术栈
```
Vue 3          # 渐进式JavaScript框架，Composition API
TypeScript     # 类型安全的JavaScript超集
Vite           # 下一代前端构建工具
Pinia          # 轻量级状态管理库
Vue Router     # 官方路由管理器
Element Plus   # 企业级UI组件库
ECharts        # 专业数据可视化图表库
Axios          # HTTP客户端
```

### 后端技术栈
```
FastAPI        # 现代、高性能Python Web框架
SQLAlchemy     # 强大的Python SQL工具包和ORM
PostgreSQL     # 企业级关系型数据库
Pydantic       # 数据验证和设置管理
asyncpg        # 异步PostgreSQL驱动
python-jose    # JWT令牌处理
passlib        # 密码加密
```

### 数据库设计
- 📊 复用TunshuEdu主系统数据库
- 👥 38个真实用户数据
- 🗄️ 27个数据表结构
- 🔗 完整的用户关系映射

## 🚀 快速开始

### 环境要求
- Node.js 16+
- Python 3.9+
- PostgreSQL 数据库访问权限

### 1️⃣ 克隆项目
```bash
git clone <repository-url>
cd TunshuEdu_BMS
```

### 2️⃣ 一键启动（推荐）
```bash
# 智能启动脚本（自动端口清理、环境检查、依赖安装、服务监控）
./start.sh
```

### 3️⃣ 手动启动
如果需要手动启动，可以分别启动前后端：

**后端启动：**
```bash
cd backend
# 创建并激活虚拟环境
python3 -m venv venv
source venv/bin/activate
# 安装依赖并启动
pip install -r requirements.txt
python3 -m app.main
```

**前端启动：**
```bash
cd frontend
npm install
npm run dev
```

### 4️⃣ 访问系统
- 🌐 **前端管理界面**: http://localhost:3033
- 🔧 **后端API服务**: http://localhost:8088
- 📚 **API文档**: http://localhost:8088/docs
- 📖 **ReDoc文档**: http://localhost:8088/redoc

## 🔐 登录账户

使用TunshuEdu系统中的管理员账户登录：

```bash
用户名: [管理员用户名]
密码: [管理员密码]
```

> ⚠️ **注意**: 只有角色为 `admin` 或 `super_admin` 的用户才能登录BMS系统

## 📊 系统截图

### Dashboard首页
![Dashboard](docs/images/dashboard.png)

### 登录界面
![Login](docs/images/login.png)

## 🏗️ 项目结构

```
TunshuEdu_BMS/
├── frontend/                    # 前端项目
│   ├── src/
│   │   ├── components/          # 可复用组件
│   │   │   └── dashboard/       # Dashboard专用组件
│   │   │       ├── StatsCard.vue      # 统计卡片
│   │   │       ├── UserActivityChart.vue  # 用户活动图表
│   │   │       └── RecentUsersTable.vue   # 用户表格
│   │   ├── views/               # 页面组件
│   │   │   ├── LoginView.vue    # 登录页面
│   │   │   ├── LayoutView.vue   # 主布局
│   │   │   └── DashboardView.vue # 仪表盘页面
│   │   ├── stores/              # Pinia状态管理
│   │   │   ├── auth.ts          # 认证状态
│   │   │   └── dashboard.ts     # Dashboard状态
│   │   ├── utils/               # 工具函数
│   │   ├── types/               # TypeScript类型定义
│   │   └── router/              # 路由配置
│   └── package.json
│
├── backend/                     # 后端项目
│   ├── app/
│   │   ├── api/                 # API路由
│   │   │   ├── auth.py          # 认证API
│   │   │   └── dashboard.py     # Dashboard API
│   │   ├── core/                # 核心模块
│   │   │   ├── config.py        # 配置管理
│   │   │   ├── database.py      # 数据库连接
│   │   │   ├── security.py      # 安全工具
│   │   │   └── dependencies.py  # 依赖注入
│   │   ├── models/              # 数据模型
│   │   │   └── user.py          # 用户模型
│   │   ├── schemas/             # Pydantic模式
│   │   │   └── user.py          # 用户模式
│   │   └── main.py              # FastAPI应用
│   └── requirements.txt
│
├── database_analysis.json       # 数据库结构分析
├── analyze_db.py                # 数据库分析脚本
└── README.md                    # 项目文档
```

## 🔧 配置说明

### 数据库配置
系统默认连接到TunshuEdu的阿里云PostgreSQL数据库：

```python
# backend/app/core/config.py
POSTGRES_HOST = "pgm-bp1k176i7sid0e775o.pg.rds.aliyuncs.com"
POSTGRES_PORT = 5432
POSTGRES_DB = "tunshuedu_db"
POSTGRES_USER = "postgres"
POSTGRES_PASSWORD = "!Tunshu0305"
```

### API接口
- 🔐 **认证接口**: `/api/v1/auth/`
  - `POST /login` - 管理员登录
  - `POST /refresh` - 刷新令牌
  - `GET /me` - 获取用户信息

- 📊 **Dashboard接口**: `/api/v1/dashboard/`
  - `GET /stats` - 核心统计数据
  - `GET /user-activity` - 用户活动趋势
  - `GET /recent-users` - 最近用户列表
  - `GET /user-search` - 用户搜索

## 🎨 设计特色

### 色彩方案
- 🔵 **主色调**: #3b82f6 (现代蓝)
- 🟢 **成功色**: #10b981 (翠绿)
- 🟡 **警告色**: #f59e0b (琥珀)
- 🟣 **紫色**: #8b5cf6 (活力紫)
- 🔷 **青色**: #06b6d4 (天空蓝)

### 组件设计
- **卡片悬浮效果**: transform + box-shadow
- **渐变背景**: 135度线性渐变
- **圆角设计**: 12px统一圆角
- **阴影层次**: 多层次阴影效果

## 📈 性能优化

- ⚡ **Vite构建**: 快速热更新和构建
- 🔄 **异步请求**: 全异步API调用
- 📊 **图表优化**: ECharts按需加载
- 🎯 **组件懒加载**: 路由级别代码分割
- 💾 **状态管理**: Pinia轻量级状态管理

## 🚀 启动脚本说明

### 🔧 智能启动脚本 (`start.sh`)
**核心特性**：
- 🔍 **智能端口管理**: 自动检测并强制清理端口3033和8088的占用进程
- 🌍 **完整环境检查**: 验证Python3、Node.js、lsof等必要工具
- 📦 **自动依赖管理**: 创建虚拟环境并安装前后端依赖
- ⚡ **并发启动**: 后端和前端服务并行启动
- 📊 **实时监控**: 持续监控服务状态和端口可用性
- 🛡️ **错误恢复**: 智能错误处理和优雅的服务清理
- 🎨 **彩色输出**: 带时间戳的彩色日志输出

**使用方法**：
```bash
./start.sh
```

### 📋 脚本执行流程
```
[时间戳] 检查系统环境...
[时间戳] 清理端口占用...
[时间戳] 发现端口 8088 被占用，正在强制关闭占用进程...
[时间戳] 正在终止进程 PID: 12345 (端口: 8088)
[时间戳] 端口 8088 已成功释放
[时间戳] 端口 3033 未被占用
[时间戳] 端口清理完成！
[时间戳] 启动后端服务...
[时间戳] 已激活虚拟环境: venv
[时间戳] 检查并安装后端依赖...
[时间戳] 启动FastAPI后端服务...
[时间戳] 等待 后端API 服务启动 (端口: 8088)...
[时间戳] 后端API 服务已启动！
[时间戳] 后端服务启动成功！访问地址: http://localhost:8088
[时间戳] API文档地址: http://localhost:8088/docs
[时间戳] 启动前端服务...
[时间戳] 前端依赖已存在，跳过安装
[时间戳] 启动Vue3前端服务...
[时间戳] 等待 前端应用 服务启动 (端口: 3033)...
[时间戳] 前端应用 服务已启动！
[时间戳] 前端服务启动成功！访问地址: http://localhost:3033

======================================
   🎉 TunshuEdu BMS 启动完成！
======================================
📱 前端管理界面: http://localhost:3033
🔧 后端API服务: http://localhost:8088
📚 API文档地址: http://localhost:8088/docs
📖 ReDoc文档: http://localhost:8088/redoc

💡 使用提示:
   • 使用管理员账户登录BMS系统
   • 只有admin或super_admin角色可以访问
   • 按 Ctrl+C 停止所有服务

🔄 服务状态监控中...
```

### ⚠️ 重要说明
- **固定端口**: 前端固定使用端口3033，后端固定使用端口8088
- **端口冲突解决**: 如发现端口被占用，脚本会自动强制关闭占用进程
- **优雅退出**: 使用 `Ctrl+C` 可以安全关闭所有服务并清理进程

## 🔍 开发调试

### 前端调试
```bash
cd frontend
npm run dev        # 开发服务器
npm run build      # 生产构建
npm run type-check # 类型检查
```

### 后端调试
```bash
cd backend
# 激活虚拟环境
source venv/bin/activate
# 启动开发服务器
python3 -m app.main
```

## 📋 API文档

系统提供完整的API文档：
- **Swagger UI**: http://localhost:8088/docs
- **ReDoc**: http://localhost:8088/redoc

## 🤝 参与贡献

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🎯 路线图

- [x] 📊 Dashboard核心功能
- [x] 🔐 管理员认证系统
- [x] 📈 数据可视化图表
- [x] 👥 用户管理功能
- [ ] 📱 移动端适配优化
- [ ] 🔔 实时通知系统
- [ ] 📊 更多数据维度
- [ ] 🛠️ 系统设置管理

## 📞 联系我们

- 🏢 **项目**: TunshuEdu BMS
- 📧 **邮箱**: <EMAIL>
- 🌐 **官网**: https://tunshuedu.com

---

**TunshuEdu BMS** - 让教育管理更智能，让数据分析更高效 🎓✨