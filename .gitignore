# Dependencies
node_modules/
*/node_modules/
.pnp
.pnp.js

# Production build
dist/
build/
.next/
.nuxt/
.output/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
.venv/
env/
ENV/
env.bak/
venv.bak/
.coverage
.pytest_cache/
*.egg-info/
.tox/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea
*.swp
*.swo
*~

# IDEs
.vs/
.vscode/
.idea/

# Local env files
*.local

# Cache directories
.cache/
.parcel-cache/
.temp/
.tmp/

# OS generated files
.DS_Store?
Icon?

# Database
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup
*.orig

# Application specific
database_analysis.json
*.rdb

# Cursor AI
.cursor/

# Alembic
alembic.ini

# Project specific files
TunshuEdu.md
analyze_db.py
db_analysis.py
start.sh

# Data files
data/
案例数据/
专业数据/
院校数据/
项目数据/