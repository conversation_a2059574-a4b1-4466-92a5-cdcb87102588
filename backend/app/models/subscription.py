from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class PaymentOrder(Base):
    """支付订单模型 - 基于现有payment_orders表"""
    __tablename__ = "payment_orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String(64), unique=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user_credit_account_id = Column(Integer, ForeignKey("user_credit_accounts.id"), nullable=False)
    amount = Column(Numeric, nullable=False)
    credits = Column(Integer, nullable=False)
    payment_method = Column(String(20), nullable=False)
    package_id = Column(String(50), nullable=True)  # 套餐ID
    status = Column(String(20), default="pending", nullable=False)
    trade_no = Column(String(100), nullable=True)
    payment_time = Column(DateTime(timezone=True), nullable=True)
    refund_amount = Column(Numeric, default=0, nullable=False)
    refund_reason = Column(Text, nullable=True)
    refund_time = Column(DateTime(timezone=True), nullable=True)
    remark = Column(Text, nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=True)

class RedeemCode(Base):
    """兑换码模型 - 基于现有redeem_codes表"""
    __tablename__ = "redeem_codes"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, nullable=False)
    credits = Column(Integer, nullable=True)  # 积分兑换码
    package_id = Column(String(50), nullable=True)  # 套餐兑换码
    description = Column(String(200), nullable=True)
    max_uses = Column(Integer, default=1, nullable=False)
    used_count = Column(Integer, default=0, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

class RedeemCodeRedemption(Base):
    """兑换码兑换记录模型 - 基于现有redeem_code_redemptions表"""
    __tablename__ = "redeem_code_redemptions"
    
    id = Column(Integer, primary_key=True, index=True)
    redeem_code_id = Column(Integer, ForeignKey("redeem_codes.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    credits_received = Column(Integer, nullable=True)
    package_order_id = Column(Integer, ForeignKey("payment_orders.id"), nullable=True)
    transaction_id = Column(Integer, ForeignKey("credit_transactions.id"), nullable=True)
    redeemed_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

class UserCreditAccount(Base):
    """用户积分账户模型 - 基于现有user_credit_accounts表"""
    __tablename__ = "user_credit_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, nullable=False)
    credit_balance = Column(Integer, default=0, nullable=False)
    total_credits_purchased = Column(Integer, default=0, nullable=False)
    total_credits_consumed = Column(Integer, default=0, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    last_recharge_at = Column(DateTime(timezone=True), nullable=True)
    last_consumption_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=True)

class CreditTransaction(Base):
    """积分交易记录模型 - 基于现有credit_transactions表"""
    __tablename__ = "credit_transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user_credit_account_id = Column(Integer, ForeignKey("user_credit_accounts.id"), nullable=False)
    transaction_type = Column(String(20), nullable=False)  # charge, consume, refund
    amount = Column(Integer, nullable=False)
    balance_before = Column(Integer, nullable=False)
    balance_after = Column(Integer, nullable=False)
    order_id = Column(Integer, ForeignKey("payment_orders.id"), nullable=True)
    service_type = Column(String(50), nullable=True)
    operation_type = Column(String(100), nullable=True)
    request_id = Column(String(100), nullable=True)
    tokens_consumed = Column(Integer, nullable=True)
    conversion_rate = Column(Integer, default=100, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)


class PackageConfig(Base):
    """套餐配置模型 - 基于packages_config表"""
    __tablename__ = "packages_config"

    id = Column(Integer, primary_key=True, index=True)
    package_id = Column(String(50), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    amount = Column(Numeric(10, 2), nullable=False)
    credits = Column(Integer, nullable=False)
    bonus = Column(Integer, default=0, nullable=False)
    billing_cycle = Column(String(20), nullable=False)  # trial, yearly, monthly
    monthly_price = Column(Numeric(10, 2), nullable=True)
    min_users = Column(Integer, default=1, nullable=False)
    max_users = Column(Integer, default=1, nullable=False)
    validity_days = Column(Integer, nullable=True)  # 体验套餐有效期天数
    is_trial = Column(Boolean, default=False, nullable=False)
    contact_required = Column(Boolean, default=False, nullable=False)
    contact_phone = Column(String(20), nullable=True)
    features = Column(JSON, nullable=True)  # 套餐功能特性列表
    bulk_discount = Column(JSON, nullable=True)  # 批量折扣配置
    is_active = Column(Boolean, default=True, nullable=False)
    sort_order = Column(Integer, default=0, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)