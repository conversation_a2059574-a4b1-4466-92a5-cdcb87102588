from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Text
from sqlalchemy.sql import func
from app.core.database import Base

class User(Base):
    """用户模型 - 基于现有users表结构"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(64), unique=True, index=True, nullable=False)
    email = Column(String(120), unique=True, index=True, nullable=False)
    nickname = Column(String(64), nullable=True)
    password_hash = Column(String(128), nullable=True)
    role = Column(String(20), default="user", nullable=True)
    is_active = Column(Boolean, default=True, nullable=True)
    
    # 微信登录相关字段
    openid = Column(String(64), nullable=True)
    unionid = Column(String(64), nullable=True)
    wechat_nickname = Column(String(64), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    login_type = Column(String(20), default="password", nullable=True)
    wechat_refresh_token = Column(String(255), nullable=True)
    
    # 时间戳
    last_login = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=True)
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"