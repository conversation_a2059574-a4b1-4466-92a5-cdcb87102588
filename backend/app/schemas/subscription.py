from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal

# 支付订单相关schemas
class PaymentOrderBase(BaseModel):
    order_no: str = Field(..., description="订单号")
    user_id: int = Field(..., description="用户ID")
    amount: Decimal = Field(..., description="支付金额")
    credits: int = Field(..., description="积分数量")
    payment_method: str = Field(..., description="支付方式")
    package_id: Optional[str] = Field(None, description="套餐ID")
    status: str = Field(default="pending", description="订单状态")
    remark: Optional[str] = Field(None, description="备注")

class PaymentOrderCreate(PaymentOrderBase):
    user_credit_account_id: int = Field(..., description="用户积分账户ID")
    expires_at: datetime = Field(..., description="订单过期时间")

class PaymentOrderUpdate(BaseModel):
    status: Optional[str] = Field(None, description="订单状态")
    trade_no: Optional[str] = Field(None, description="交易号")
    payment_time: Optional[datetime] = Field(None, description="支付时间")
    refund_amount: Optional[Decimal] = Field(None, description="退款金额")
    refund_reason: Optional[str] = Field(None, description="退款原因")
    refund_time: Optional[datetime] = Field(None, description="退款时间")
    remark: Optional[str] = Field(None, description="备注")

class PaymentOrderResponse(PaymentOrderBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_credit_account_id: int
    trade_no: Optional[str] = None
    payment_time: Optional[datetime] = None
    refund_amount: Decimal
    refund_reason: Optional[str] = None
    refund_time: Optional[datetime] = None
    expires_at: datetime
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # 关联用户信息
    user_username: Optional[str] = None
    user_email: Optional[str] = None
    user_wechat_nickname: Optional[str] = None
    user_nickname: Optional[str] = None

# 兑换码相关schemas
class RedeemCodeBase(BaseModel):
    code: str = Field(..., max_length=50, description="兑换码")
    credits: Optional[int] = Field(None, description="积分数量")
    package_id: Optional[str] = Field(None, description="套餐ID")
    description: Optional[str] = Field(None, max_length=200, description="描述")
    max_uses: int = Field(default=1, description="最大使用次数")
    is_active: bool = Field(default=True, description="是否激活")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

class RedeemCodeCreate(RedeemCodeBase):
    pass

class RedeemCodeUpdate(BaseModel):
    description: Optional[str] = Field(None, max_length=200, description="描述")
    max_uses: Optional[int] = Field(None, ge=1, description="最大使用次数")
    is_active: Optional[bool] = Field(None, description="是否激活")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

class RedeemCodeResponse(RedeemCodeBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    used_count: int
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    # 计算字段
    is_expired: bool = False
    usage_rate: float = 0.0  # 使用率

class RedeemCodeRedemptionResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    redeem_code_id: int
    user_id: int
    credits_received: Optional[int] = None
    package_order_id: Optional[int] = None
    transaction_id: Optional[int] = None
    redeemed_at: datetime
    
    # 关联信息
    redeem_code: Optional[str] = None
    user_username: Optional[str] = None
    user_email: Optional[str] = None
    user_wechat_nickname: Optional[str] = None
    user_nickname: Optional[str] = None

# 用户积分账户相关schemas
class UserCreditAccountResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    credit_balance: int
    total_credits_purchased: int
    total_credits_consumed: int
    is_active: bool
    last_recharge_at: Optional[datetime] = None
    last_consumption_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # 关联用户信息
    user_username: Optional[str] = None
    user_email: Optional[str] = None

# 积分交易记录相关schemas
class CreditTransactionResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    user_credit_account_id: int
    transaction_type: str
    amount: int
    balance_before: int
    balance_after: int
    order_id: Optional[int] = None
    service_type: Optional[str] = None
    operation_type: Optional[str] = None
    request_id: Optional[str] = None
    tokens_consumed: Optional[int] = None
    conversion_rate: int
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    
    # 关联信息
    user_username: Optional[str] = None
    order_no: Optional[str] = None

# 订阅统计相关schemas
class SubscriptionStats(BaseModel):
    total_orders: int = Field(description="总订单数")
    pending_orders: int = Field(description="待支付订单数")
    paid_orders: int = Field(description="已支付订单数")
    total_revenue: Decimal = Field(description="总收入")
    total_credits_sold: int = Field(description="总销售积分")
    active_redeem_codes: int = Field(description="活跃兑换码数")
    total_redemptions: int = Field(description="总兑换次数")
    
    # 增长率数据
    orders_growth_rate: Optional[float] = Field(default=None, description="订单增长率")
    revenue_growth_rate: Optional[float] = Field(default=None, description="收入增长率")
    credits_growth_rate: Optional[float] = Field(default=None, description="积分增长率")
    redemptions_growth_rate: Optional[float] = Field(default=None, description="兑换增长率")

# 分页相关schemas
class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")
    
class PaginatedResponse(BaseModel):
    items: List
    total: int
    page: int
    page_size: int
    pages: int


# 套餐配置相关schemas
class PackageConfigBase(BaseModel):
    package_id: str = Field(..., max_length=50, description="套餐ID")
    name: str = Field(..., max_length=100, description="套餐名称")
    description: Optional[str] = Field(None, description="套餐描述")
    amount: Decimal = Field(..., description="套餐价格（元）")
    credits: int = Field(..., description="套餐包含的积分数量")
    bonus: int = Field(default=0, description="赠送积分数量")
    billing_cycle: str = Field(..., max_length=20, description="计费周期：trial, yearly, monthly")
    monthly_price: Optional[Decimal] = Field(None, description="月均价格（元）")
    min_users: int = Field(default=1, description="最少用户数")
    max_users: int = Field(default=1, description="最多用户数")
    validity_days: Optional[int] = Field(None, description="有效期天数（体验套餐使用）")
    is_trial: bool = Field(default=False, description="是否为体验套餐")
    contact_required: bool = Field(default=False, description="是否需要联系销售")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    features: Optional[List[str]] = Field(None, description="套餐功能特性列表")
    bulk_discount: Optional[Dict[str, Any]] = Field(None, description="批量折扣配置")
    is_active: bool = Field(default=True, description="是否启用")
    sort_order: int = Field(default=0, description="排序顺序")


class PackageConfigCreate(PackageConfigBase):
    pass


class PackageConfigUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100, description="套餐名称")
    description: Optional[str] = Field(None, description="套餐描述")
    amount: Optional[Decimal] = Field(None, description="套餐价格（元）")
    credits: Optional[int] = Field(None, description="套餐包含的积分数量")
    bonus: Optional[int] = Field(None, description="赠送积分数量")
    billing_cycle: Optional[str] = Field(None, max_length=20, description="计费周期")
    monthly_price: Optional[Decimal] = Field(None, description="月均价格（元）")
    min_users: Optional[int] = Field(None, description="最少用户数")
    max_users: Optional[int] = Field(None, description="最多用户数")
    validity_days: Optional[int] = Field(None, description="有效期天数")
    is_trial: Optional[bool] = Field(None, description="是否为体验套餐")
    contact_required: Optional[bool] = Field(None, description="是否需要联系销售")
    contact_phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    features: Optional[List[str]] = Field(None, description="套餐功能特性列表")
    bulk_discount: Optional[Dict[str, Any]] = Field(None, description="批量折扣配置")
    is_active: Optional[bool] = Field(None, description="是否启用")
    sort_order: Optional[int] = Field(None, description="排序顺序")


class PackageConfigResponse(PackageConfigBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    created_at: datetime
    updated_at: datetime


# 创建自定义套餐和兑换码的组合请求
class CustomPackageRedeemCodeCreate(BaseModel):
    # 套餐配置
    package_config: PackageConfigCreate = Field(..., description="套餐配置信息")

    # 兑换码配置
    redeem_code: str = Field(..., max_length=50, description="兑换码")
    redeem_description: Optional[str] = Field(None, max_length=200, description="兑换码描述")
    max_uses: int = Field(default=1, ge=1, description="最大使用次数")
    expires_at: Optional[datetime] = Field(None, description="兑换码过期时间")


# 创建自定义套餐和兑换码的响应
class CustomPackageRedeemCodeResponse(BaseModel):
    package_config: PackageConfigResponse = Field(..., description="创建的套餐配置")
    redeem_code: RedeemCodeResponse = Field(..., description="创建的兑换码")


# 创建自定义套餐和兑换码的组合请求
class CustomPackageRedeemCodeCreate(BaseModel):
    # 套餐配置
    package_config: PackageConfigCreate = Field(..., description="套餐配置信息")

    # 兑换码配置
    redeem_code: str = Field(..., max_length=50, description="兑换码")
    redeem_description: Optional[str] = Field(None, max_length=200, description="兑换码描述")
    max_uses: int = Field(default=1, ge=1, description="最大使用次数")
    expires_at: Optional[datetime] = Field(None, description="兑换码过期时间")


# 创建自定义套餐和兑换码的响应
class CustomPackageRedeemCodeResponse(BaseModel):
    package_config: PackageConfigResponse = Field(..., description="创建的套餐配置")
    redeem_code: RedeemCodeResponse = Field(..., description="创建的兑换码")