from pydantic import BaseModel, EmailStr, Field, field_serializer
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """用户基础模式"""
    username: str = Field(..., min_length=3, max_length=64)
    email: EmailStr
    nickname: Optional[str] = None
    role: str = "user"
    is_active: bool = True

class UserCreate(UserBase):
    """创建用户模式"""
    password: str = Field(..., min_length=6)

class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str

class UserUpdate(BaseModel):
    """用户更新模式"""
    nickname: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None

class UserInDB(UserBase):
    """数据库中的用户模式"""
    id: int
    password_hash: Optional[str] = None
    openid: Optional[str] = None
    unionid: Optional[str] = None
    wechat_nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    login_type: str = "password"
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class UserResponse(BaseModel):
    """用户响应模式"""
    id: int
    username: str
    email: str
    nickname: Optional[str] = None
    role: str
    is_active: bool
    avatar_url: Optional[str] = None
    login_type: str
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    """JWT令牌模式"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class TokenData(BaseModel):
    """令牌数据模式"""
    username: Optional[str] = None
    user_id: Optional[int] = None

# Dashboard相关模式
class DashboardStats(BaseModel):
    """仪表盘统计数据"""
    total_users: int
    today_new_users: int
    today_growth_rate: float
    yesterday_active_users: int
    week_active_users: int
    month_active_users: int

class UserActivity(BaseModel):
    """用户活动数据"""
    date: str
    new_users: int
    active_users: int

class RecentUser(BaseModel):
    """最近用户信息"""
    id: int
    username: str
    nickname: Optional[str] = None
    wechat_nickname: Optional[str] = None
    email: str
    login_type: str
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    
    @field_serializer('last_login', 'created_at')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        """确保datetime以ISO格式序列化，包含Z后缀表示UTC时间"""
        if value is None:
            return None
        # 确保输出ISO格式的UTC时间
        return value.isoformat() + 'Z' if not value.isoformat().endswith('Z') else value.isoformat()
    
    class Config:
        from_attributes = True