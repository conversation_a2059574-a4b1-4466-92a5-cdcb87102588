from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from app.core.config import settings
from app.core.database import init_database
from app.api import auth, dashboard, subscription, user_management

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    await init_database()
    print("🚀 TunshuEdu BMS 后端服务启动成功")
    yield
    # 关闭时执行
    print("📴 TunshuEdu BMS 后端服务关闭")

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="TunshuEdu 后台管理系统API",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(auth.router, prefix=settings.api_v1_prefix)
app.include_router(dashboard.router, prefix=settings.api_v1_prefix)
app.include_router(subscription.router, prefix=settings.api_v1_prefix)
app.include_router(user_management.router, prefix=settings.api_v1_prefix)

@app.get("/", summary="根路径")
async def root():
    """根路径健康检查"""
    return {
        "message": "TunshuEdu BMS API",
        "version": settings.app_version,
        "status": "running"
    }

@app.get("/health", summary="健康检查")
async def health_check():
    """API健康检查"""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.app_version
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8088,
        reload=settings.debug
    )