from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    """应用配置设置"""
    
    # 应用基本信息
    app_name: str = "TunshuEdu BMS"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置 - 使用TunshuEdu的阿里云PostgreSQL
    postgres_host: str = "pgm-bp1k176i7sid0e775o.pg.rds.aliyuncs.com"
    postgres_port: int = 5432
    postgres_user: str = "postgres"
    postgres_password: str = "!Tunshu0305"
    postgres_db: str = "tunshuedu_db"
    
    # postgres_host: str = "localhost"
    # postgres_port: int = 5432
    # postgres_user: str = "postgres"
    # postgres_password: str = "admin123"
    # postgres_db: str = "tunshuedu_db"
    
    # JWT认证配置
    secret_key: str = "bms-super-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # CORS配置
    allowed_origins: list = ["http://localhost:3033", "http://localhost:5173", "http://localhost:8080"]
    
    # API配置
    api_v1_prefix: str = "/api/v1"
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"postgresql+asyncpg://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# 创建全局设置实例
settings = Settings()