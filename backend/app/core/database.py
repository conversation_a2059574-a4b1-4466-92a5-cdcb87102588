from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from app.core.config import settings

# 创建异步数据库引擎
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=False,
    pool_recycle=3600,
    connect_args={"ssl": "disable"}  # 阿里云数据库配置
)

# 创建会话工厂
async_session_maker = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 基础模型类
class Base(DeclarativeBase):
    pass

async def get_database_session() -> AsyncSession:
    """获取数据库会话"""
    async with async_session_maker() as session:
        try:
            yield session
        finally:
            await session.close()

async def init_database():
    """初始化数据库"""
    async with engine.begin() as conn:
        # 由于使用现有数据库，这里不需要创建表
        # await conn.run_sync(Base.metadata.create_all)
        pass