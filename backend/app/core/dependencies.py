from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database import get_database_session
from app.core.security import verify_token
from app.models.user import User
from app.schemas.user import UserInDB

# HTTP Bearer认证方案
security = HTTPBearer()

async def get_db() -> AsyncSession:
    """获取数据库会话依赖"""
    async for session in get_database_session():
        yield session

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> UserInDB:
    """获取当前认证用户"""
    
    # 验证JWT令牌
    token_data = verify_token(credentials.credentials)
    
    # 从数据库获取用户信息
    stmt = select(User).where(User.id == token_data["user_id"])
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已禁用"
        )
    
    return UserInDB.model_validate(user)

async def get_current_admin_user(
    current_user: UserInDB = Depends(get_current_user)
) -> UserInDB:
    """获取当前管理员用户"""
    if current_user.role not in ["admin", "super_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    return current_user