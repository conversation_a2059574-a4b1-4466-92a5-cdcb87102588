from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, text
from datetime import datetime, timedelta, timezone
from typing import List
from app.core.dependencies import get_db, get_current_admin_user
from app.models.user import User
from app.schemas.user import (
    DashboardStats, 
    UserActivity, 
    RecentUser, 
    UserInDB
)

router = APIRouter(prefix="/dashboard", tags=["仪表盘"])

@router.get("/stats", response_model=DashboardStats, summary="获取核心运营指标")
async def get_dashboard_stats(
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取仪表盘核心运营指标卡片数据
    
    返回包含用户增长统计的核心指标：
    - 总注册用户数
    - 今日新增用户数和增长率
    - 昨日、近7日、近30日活跃用户数
    """
    
    # 使用北京时间（UTC+8）来计算时间范围
    beijing_tz = timezone(timedelta(hours=8))
    now_beijing = datetime.now(beijing_tz)
    today_start_beijing = now_beijing.replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday_start_beijing = today_start_beijing - timedelta(days=1)
    
    # 转换为UTC时间用于数据库查询
    today_start = today_start_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    yesterday_start = yesterday_start_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    week_ago = today_start - timedelta(days=7)
    month_ago = today_start - timedelta(days=30)
    
    # 总注册用户数
    total_users_stmt = select(func.count(User.id))
    total_users_result = await db.execute(total_users_stmt)
    total_users = total_users_result.scalar() or 0
    
    # 今日新增用户数
    today_new_users_stmt = select(func.count(User.id)).where(
        User.created_at >= today_start
    )
    today_new_users_result = await db.execute(today_new_users_stmt)
    today_new_users = today_new_users_result.scalar() or 0
    
    # 昨日新增用户数（用于计算增长率）
    yesterday_new_users_stmt = select(func.count(User.id)).where(
        and_(
            User.created_at >= yesterday_start,
            User.created_at < today_start
        )
    )
    yesterday_new_users_result = await db.execute(yesterday_new_users_stmt)
    yesterday_new_users = yesterday_new_users_result.scalar() or 0
    
    # 计算增长率
    if yesterday_new_users > 0:
        growth_rate = ((today_new_users - yesterday_new_users) / yesterday_new_users) * 100
    else:
        growth_rate = 100.0 if today_new_users > 0 else 0.0
    
    # 昨日活跃用户数（最后登录时间在昨日的用户）
    yesterday_active_stmt = select(func.count(User.id)).where(
        and_(
            User.last_login >= yesterday_start,
            User.last_login < today_start,
            User.is_active == True
        )
    )
    yesterday_active_result = await db.execute(yesterday_active_stmt)
    yesterday_active_users = yesterday_active_result.scalar() or 0
    
    # 近7日活跃用户数
    week_active_stmt = select(func.count(User.id)).where(
        and_(
            User.last_login >= week_ago,
            User.is_active == True
        )
    )
    week_active_result = await db.execute(week_active_stmt)
    week_active_users = week_active_result.scalar() or 0
    
    # 近30日活跃用户数
    month_active_stmt = select(func.count(User.id)).where(
        and_(
            User.last_login >= month_ago,
            User.is_active == True
        )
    )
    month_active_result = await db.execute(month_active_stmt)
    month_active_users = month_active_result.scalar() or 0
    
    return DashboardStats(
        total_users=total_users,
        today_new_users=today_new_users,
        today_growth_rate=round(growth_rate, 1),
        yesterday_active_users=yesterday_active_users,
        week_active_users=week_active_users,
        month_active_users=month_active_users
    )

@router.get("/user-activity", response_model=List[UserActivity], summary="获取用户活动趋势")
async def get_user_activity(
    days: int = Query(default=30, ge=7, le=365, description="查询天数"),
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户活动趋势数据（用于图表展示）
    
    - **days**: 查询天数，默认30天，支持7-365天
    
    返回指定天数内每日的新增用户数和活跃用户数
    """
    
    # 使用北京时间（UTC+8）来计算时间范围
    beijing_tz = timezone(timedelta(hours=8))
    now_beijing = datetime.now(beijing_tz)
    end_date_beijing = now_beijing.replace(hour=0, minute=0, second=0, microsecond=0)
    start_date_beijing = end_date_beijing - timedelta(days=days)
    
    # 转换为UTC时间用于数据库查询
    end_date = end_date_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    start_date = start_date_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    
    # 使用SQLAlchemy ORM查询替代复杂的原生SQL
    # 首先获取所有日期范围内的用户数据
    from collections import defaultdict
    
    # 获取新增用户数据 - 修复时区问题
    # 使用 PostgreSQL 的 AT TIME ZONE 语法将 UTC 时间转换为北京时间进行日期分组
    # 使用 CTE 来避免 GROUP BY 问题
    new_users_stmt = (
        select(
            func.date(
                User.created_at + text("INTERVAL '8 hours'")
            ).label('date'),
            func.count(User.id).label('new_users')
        )
        .where(
            and_(
                User.created_at >= start_date,
                User.created_at < end_date
            )
        )
        .group_by(
            func.date(
                User.created_at + text("INTERVAL '8 hours'")
            )
        )
    )
    
    new_users_result = await db.execute(new_users_stmt)
    new_users_data = {row.date: row.new_users for row in new_users_result}
    
    # 获取活跃用户数据 - 修复时区问题
    # 使用简单的时间偏移来避免复杂的时区函数问题
    active_users_stmt = (
        select(
            func.date(
                User.last_login + text("INTERVAL '8 hours'")
            ).label('date'),
            func.count(User.id).label('active_users')
        )
        .where(
            and_(
                User.last_login >= start_date,
                User.last_login < end_date,
                User.is_active == True
            )
        )
        .group_by(
            func.date(
                User.last_login + text("INTERVAL '8 hours'")
            )
        )
    )
    
    active_users_result = await db.execute(active_users_stmt)
    active_users_data = {row.date: row.active_users for row in active_users_result}
    
    # 生成完整的日期范围数据（使用北京时间）
    activity_data = []
    current_date_beijing = start_date_beijing.date()
    end_date_beijing_only = end_date_beijing.date()
    
    while current_date_beijing < end_date_beijing_only:
        # 现在数据库查询已经按北京时间分组，直接使用北京时间日期匹配
        activity_data.append(UserActivity(
            date=current_date_beijing.strftime("%Y-%m-%d"),
            new_users=new_users_data.get(current_date_beijing, 0),
            active_users=active_users_data.get(current_date_beijing, 0)
        ))
        current_date_beijing += timedelta(days=1)
    
    return activity_data


@router.get("/debug/user-activity", summary="调试用户活动数据")
async def debug_user_activity(
    target_date: str = Query(..., description="目标日期，格式：YYYY-MM-DD"),
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    调试特定日期的用户活动数据，用于验证时区修复效果
    """
    from datetime import datetime as dt

    # 解析目标日期
    try:
        target_date_obj = dt.strptime(target_date, "%Y-%m-%d").date()
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误，请使用 YYYY-MM-DD")

    # 使用北京时间计算日期范围
    beijing_tz = timezone(timedelta(hours=8))
    target_start_beijing = datetime.combine(target_date_obj, datetime.min.time()).replace(tzinfo=beijing_tz)
    target_end_beijing = target_start_beijing + timedelta(days=1)

    # 转换为UTC时间用于数据库查询
    target_start_utc = target_start_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    target_end_utc = target_end_beijing.astimezone(timezone.utc).replace(tzinfo=None)

    # 查询该日期范围内的所有用户登录记录
    users_stmt = select(
        User.id,
        User.username,
        User.last_login,
        (User.last_login + text("INTERVAL '8 hours'")).label('last_login_beijing')
    ).where(
        and_(
            User.last_login >= target_start_utc,
            User.last_login < target_end_utc,
            User.is_active == True
        )
    ).order_by(User.last_login)

    users_result = await db.execute(users_stmt)
    users_data = users_result.all()

    # 统计活跃用户数（使用修复后的查询）
    active_count_stmt = select(
        func.count(User.id)
    ).where(
        and_(
            func.date(User.last_login + text("INTERVAL '8 hours'")) == target_date_obj,
            User.is_active == True
        )
    )

    active_count_result = await db.execute(active_count_stmt)
    active_count = active_count_result.scalar() or 0

    # 统计用户管理页面显示的用户数（所有在该日期有登录记录的用户）
    management_count_stmt = select(
        func.count(User.id)
    ).where(
        and_(
            User.last_login >= target_start_utc,
            User.last_login < target_end_utc
        )
    )

    management_count_result = await db.execute(management_count_stmt)
    management_count = management_count_result.scalar() or 0

    return {
        "target_date": target_date,
        "beijing_time_range": {
            "start": target_start_beijing.isoformat(),
            "end": target_end_beijing.isoformat()
        },
        "utc_time_range": {
            "start": target_start_utc.isoformat(),
            "end": target_end_utc.isoformat()
        },
        "active_users_count_fixed": active_count,
        "management_display_count": management_count,
        "user_details": [
            {
                "id": user.id,
                "username": user.username,
                "last_login_utc": user.last_login.isoformat() if user.last_login else None,
                "last_login_beijing": user.last_login_beijing.isoformat() if user.last_login_beijing else None
            }
            for user in users_data
        ]
    }

@router.get("/recent-users", response_model=List[RecentUser], summary="获取最近用户列表")
async def get_recent_users(
    limit: int = Query(default=20, ge=1, le=100, description="返回用户数量"),
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取最近注册的用户列表
    
    - **limit**: 返回用户数量，默认20个，最多100个
    
    返回按注册时间降序排列的用户列表
    """
    
    stmt = (
        select(User)
        .where(User.is_active == True)
        .order_by(User.created_at.desc())
        .limit(limit)
    )
    
    result = await db.execute(stmt)
    users = result.scalars().all()
    
    recent_users = []
    for user in users:
        recent_users.append(RecentUser(
            id=user.id,
            username=user.username,
            nickname=user.nickname,
            wechat_nickname=user.wechat_nickname,
            email=user.email,
            login_type=user.login_type or "password",
            last_login=user.last_login,
            created_at=user.created_at
        ))
    
    return recent_users

@router.get("/user-search", response_model=List[RecentUser], summary="搜索用户")
async def search_users(
    query: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(default=20, ge=1, le=100, description="返回用户数量"),
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    根据微信昵称、昵称、用户名或邮箱搜索用户
    
    - **query**: 搜索关键词（微信昵称、昵称、用户名或邮箱）
    - **limit**: 返回用户数量
    """
    
    search_pattern = f"%{query}%"
    stmt = (
        select(User)
        .where(
            and_(
                User.is_active == True,
                (
                    User.wechat_nickname.ilike(search_pattern) |
                    User.nickname.ilike(search_pattern) |
                    User.username.ilike(search_pattern) |
                    User.email.ilike(search_pattern)
                )
            )
        )
        .order_by(User.created_at.desc())
        .limit(limit)
    )
    
    result = await db.execute(stmt)
    users = result.scalars().all()
    
    search_results = []
    for user in users:
        search_results.append(RecentUser(
            id=user.id,
            username=user.username,
            nickname=user.nickname,
            wechat_nickname=user.wechat_nickname,
            email=user.email,
            login_type=user.login_type or "password",
            last_login=user.last_login,
            created_at=user.created_at
        ))
    
    return search_results