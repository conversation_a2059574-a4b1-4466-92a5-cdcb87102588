from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, asc
from typing import List, Optional, Literal
from datetime import datetime, timedelta
import math

from app.core.dependencies import get_db, get_current_admin_user
from app.models.user import User
from app.schemas.user import UserInDB, RecentUser
from app.schemas.subscription import PaginatedResponse

router = APIRouter(prefix="/user-management", tags=["用户管理"])

# 用户详细信息响应模型
from pydantic import BaseModel

class UserDetailResponse(BaseModel):
    """用户详细信息响应"""
    id: int
    username: str
    email: str
    nickname: Optional[str] = None
    wechat_nickname: Optional[str] = None
    role: str
    is_active: bool
    avatar_url: Optional[str] = None
    login_type: str
    openid: Optional[str] = None
    unionid: Optional[str] = None
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class UserStatsResponse(BaseModel):
    """用户统计信息响应"""
    total_users: int
    active_users: int
    inactive_users: int
    admin_users: int
    regular_users: int
    wechat_users: int
    password_users: int
    today_registered: int
    this_week_registered: int
    this_month_registered: int

@router.get("/stats", response_model=UserStatsResponse, summary="获取用户统计信息")
async def get_user_stats(
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户统计信息"""
    
    now = datetime.utcnow()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    week_start = today_start - timedelta(days=today_start.weekday())
    month_start = today_start.replace(day=1)
    
    # 总用户数
    total_users_result = await db.execute(select(func.count(User.id)))
    total_users = total_users_result.scalar() or 0
    
    # 活跃用户数
    active_users_result = await db.execute(
        select(func.count(User.id)).where(User.is_active == True)
    )
    active_users = active_users_result.scalar() or 0
    
    # 非活跃用户数
    inactive_users = total_users - active_users
    
    # 管理员用户数
    admin_users_result = await db.execute(
        select(func.count(User.id)).where(User.role == "admin")
    )
    admin_users = admin_users_result.scalar() or 0
    
    # 普通用户数
    regular_users = total_users - admin_users
    
    # 微信登录用户数
    wechat_users_result = await db.execute(
        select(func.count(User.id)).where(User.login_type == "wechat")
    )
    wechat_users = wechat_users_result.scalar() or 0
    
    # 密码登录用户数
    password_users_result = await db.execute(
        select(func.count(User.id)).where(
            or_(User.login_type == "password", User.login_type.is_(None))
        )
    )
    password_users = password_users_result.scalar() or 0
    
    # 今日注册用户数
    today_registered_result = await db.execute(
        select(func.count(User.id)).where(User.created_at >= today_start)
    )
    today_registered = today_registered_result.scalar() or 0
    
    # 本周注册用户数
    week_registered_result = await db.execute(
        select(func.count(User.id)).where(User.created_at >= week_start)
    )
    this_week_registered = week_registered_result.scalar() or 0
    
    # 本月注册用户数
    month_registered_result = await db.execute(
        select(func.count(User.id)).where(User.created_at >= month_start)
    )
    this_month_registered = month_registered_result.scalar() or 0
    
    return UserStatsResponse(
        total_users=total_users,
        active_users=active_users,
        inactive_users=inactive_users,
        admin_users=admin_users,
        regular_users=regular_users,
        wechat_users=wechat_users,
        password_users=password_users,
        today_registered=today_registered,
        this_week_registered=this_week_registered,
        this_month_registered=this_month_registered
    )

@router.get("/users", response_model=PaginatedResponse, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(25, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词(用户名/邮箱/昵称/微信昵称)"),
    role: Optional[str] = Query(None, description="角色筛选"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    login_type: Optional[str] = Query(None, description="登录方式筛选"),
    sort_by: Optional[str] = Query("created_at", description="排序字段"),
    sort_order: Optional[Literal["asc", "desc"]] = Query("desc", description="排序方向"),
    start_date: Optional[datetime] = Query(None, description="注册开始时间"),
    end_date: Optional[datetime] = Query(None, description="注册结束时间"),
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表，支持分页、搜索、筛选和排序"""
    
    # 构建基础查询
    stmt = select(User)
    count_stmt = select(func.count(User.id))
    
    # 添加搜索条件
    conditions = []
    
    if search:
        search_pattern = f"%{search}%"
        search_condition = or_(
            User.username.ilike(search_pattern),
            User.email.ilike(search_pattern),
            User.nickname.ilike(search_pattern),
            User.wechat_nickname.ilike(search_pattern)
        )
        conditions.append(search_condition)
    
    if role is not None:
        conditions.append(User.role == role)
    
    if is_active is not None:
        conditions.append(User.is_active == is_active)
    
    if login_type is not None:
        conditions.append(User.login_type == login_type)
    
    if start_date is not None:
        conditions.append(User.created_at >= start_date)
    
    if end_date is not None:
        conditions.append(User.created_at <= end_date)
    
    # 应用筛选条件
    if conditions:
        where_clause = and_(*conditions)
        stmt = stmt.where(where_clause)
        count_stmt = count_stmt.where(where_clause)
    
    # 获取总数
    total_result = await db.execute(count_stmt)
    total = total_result.scalar() or 0
    
    # 计算分页
    pages = math.ceil(total / page_size)
    offset = (page - 1) * page_size
    
    # 添加排序
    if sort_by:
        if hasattr(User, sort_by):
            order_column = getattr(User, sort_by)
            if sort_order == "desc":
                stmt = stmt.order_by(desc(order_column))
            else:
                stmt = stmt.order_by(asc(order_column))
        else:
            # 默认按创建时间倒序
            stmt = stmt.order_by(desc(User.created_at))
    else:
        stmt = stmt.order_by(desc(User.created_at))
    
    # 添加分页
    stmt = stmt.offset(offset).limit(page_size)
    
    # 执行查询
    result = await db.execute(stmt)
    users = result.scalars().all()
    
    # 转换为响应格式
    user_list = []
    for user in users:
        user_list.append(UserDetailResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            nickname=user.nickname,
            wechat_nickname=user.wechat_nickname,
            role=user.role or "user",
            is_active=user.is_active if user.is_active is not None else True,
            avatar_url=user.avatar_url,
            login_type=user.login_type or "password",
            openid=user.openid,
            unionid=user.unionid,
            last_login=user.last_login,
            created_at=user.created_at,
            updated_at=user.updated_at
        ))
    
    return PaginatedResponse(
        items=user_list,
        total=total,
        page=page,
        page_size=page_size,
        pages=pages
    )

@router.get("/users/{user_id}", response_model=UserDetailResponse, summary="获取用户详情")
async def get_user_detail(
    user_id: int,
    current_user: UserInDB = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定用户的详细信息"""
    
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    return UserDetailResponse(
        id=user.id,
        username=user.username,
        email=user.email,
        nickname=user.nickname,
        wechat_nickname=user.wechat_nickname,
        role=user.role or "user",
        is_active=user.is_active if user.is_active is not None else True,
        avatar_url=user.avatar_url,
        login_type=user.login_type or "password",
        openid=user.openid,
        unionid=user.unionid,
        last_login=user.last_login,
        created_at=user.created_at,
        updated_at=user.updated_at
    ) 