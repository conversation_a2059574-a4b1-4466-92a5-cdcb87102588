from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from typing import List, Optional
from datetime import datetime, timedelta, timezone
import math

from app.core.dependencies import get_db
from app.core.dependencies import get_current_admin_user
from app.models.user import User
from app.models.subscription import (
    PaymentOrder, RedeemCode, RedeemCodeRedemption,
    UserCreditAccount, CreditTransaction, PackageConfig
)
from app.schemas.subscription import (
    PaymentOrderResponse, PaymentOrderUpdate,
    RedeemCodeResponse, RedeemCodeCreate, RedeemCodeUpdate,
    RedeemCodeRedemptionResponse, UserCreditAccountResponse,
    CreditTransactionResponse, SubscriptionStats,
    PaginationParams, PaginatedResponse,
    PackageConfigResponse, PackageConfigCreate, PackageConfigUpdate,
    CustomPackageRedeemCodeCreate, CustomPackageRedeemCodeResponse
)
from app.schemas.user import UserResponse

router = APIRouter(prefix="/subscription", tags=["订阅管理"])


def is_datetime_expired(expires_at: datetime) -> bool:
    """
    安全地检查 datetime 是否已过期
    处理 timezone-aware 和 timezone-naive datetime 的比较
    """
    if expires_at is None:
        return False

    # 如果是 naive datetime，假设它是 UTC 时间
    if expires_at.tzinfo is None:
        expires_at = expires_at.replace(tzinfo=timezone.utc)

    return expires_at < datetime.now(timezone.utc)

# 支付订单管理
@router.get("/orders", response_model=PaginatedResponse)
async def get_payment_orders(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="订单状态筛选"),
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    payment_method: Optional[str] = Query(None, description="支付方式筛选"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    search: Optional[str] = Query(None, description="搜索关键词(订单号/用户名/邮箱)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取支付订单列表"""
    
    # 构建查询
    query = select(
        PaymentOrder,
        User.username,
        User.email,
        User.wechat_nickname,
        User.nickname
    ).join(User, PaymentOrder.user_id == User.id)
    
    # 添加筛选条件
    conditions = []
    # 排除兑换码类型的订单
    conditions.append(PaymentOrder.payment_method != 'redeem_code_free')
    
    if status:
        conditions.append(PaymentOrder.status == status)
    if user_id:
        conditions.append(PaymentOrder.user_id == user_id)
    if payment_method:
        conditions.append(PaymentOrder.payment_method == payment_method)
    if start_date:
        conditions.append(PaymentOrder.created_at >= start_date)
    if end_date:
        conditions.append(PaymentOrder.created_at <= end_date)
    if search:
        conditions.append(
            or_(
                PaymentOrder.order_no.ilike(f"%{search}%"),
                User.username.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%")
            )
        )
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # 获取总数
    count_query = select(func.count(PaymentOrder.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    query = query.order_by(desc(PaymentOrder.created_at))
    query = query.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(query)
    rows = result.all()
    
    # 构建响应数据
    items = []
    for row in rows:
        order, username, email, wechat_nickname, nickname = row
        order_dict = {
            **order.__dict__,
            'user_username': username,
            'user_email': email,
            'user_wechat_nickname': wechat_nickname,
            'user_nickname': nickname
        }
        items.append(PaymentOrderResponse.model_validate(order_dict))
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=math.ceil(total / page_size)
    )

@router.get("/orders/{order_id}", response_model=PaymentOrderResponse)
async def get_payment_order(
    order_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取支付订单详情"""
    
    query = select(
        PaymentOrder,
        User.username,
        User.email
    ).join(User, PaymentOrder.user_id == User.id).where(PaymentOrder.id == order_id)
    
    result = await db.execute(query)
    row = result.first()
    
    if not row:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    order, username, email = row
    order_dict = {
        **order.__dict__,
        'user_username': username,
        'user_email': email
    }
    
    return PaymentOrderResponse.model_validate(order_dict)

@router.put("/orders/{order_id}", response_model=PaymentOrderResponse)
async def update_payment_order(
    order_id: int,
    order_update: PaymentOrderUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新支付订单"""
    
    # 获取订单
    result = await db.execute(select(PaymentOrder).where(PaymentOrder.id == order_id))
    order = result.scalar_one_or_none()
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 更新字段
    update_data = order_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(order, field, value)
    
    order.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(order)
    
    return await get_payment_order(order_id, db, current_user)

# 兑换码管理
@router.get("/redeem-codes", response_model=PaginatedResponse)
async def get_redeem_codes(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选"),
    code_type: Optional[str] = Query(None, description="兑换码类型(credits/package)"),
    search: Optional[str] = Query(None, description="搜索关键词(兑换码/描述)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取兑换码列表"""
    
    query = select(RedeemCode)
    
    # 添加筛选条件
    conditions = []
    if is_active is not None:
        conditions.append(RedeemCode.is_active == is_active)
    if code_type == "credits":
        conditions.append(RedeemCode.credits.isnot(None))
    elif code_type == "package":
        conditions.append(RedeemCode.package_id.isnot(None))
    if search:
        conditions.append(
            or_(
                RedeemCode.code.ilike(f"%{search}%"),
                RedeemCode.description.ilike(f"%{search}%")
            )
        )
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # 获取总数
    count_query = select(func.count(RedeemCode.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    query = query.order_by(desc(RedeemCode.created_at))
    query = query.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(query)
    redeem_codes = result.scalars().all()
    
    # 构建响应数据
    items = []
    for code in redeem_codes:
        code_dict = code.__dict__.copy()
        # 计算是否过期
        code_dict['is_expired'] = is_datetime_expired(code.expires_at)
        # 计算使用率
        code_dict['usage_rate'] = (
            code.used_count / code.max_uses * 100 
            if code.max_uses > 0 else 0
        )
        items.append(RedeemCodeResponse.model_validate(code_dict))
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=math.ceil(total / page_size)
    )

@router.post("/redeem-codes", response_model=RedeemCodeResponse)
async def create_redeem_code(
    redeem_code: RedeemCodeCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """创建兑换码"""
    
    # 检查兑换码是否已存在
    existing = await db.execute(
        select(RedeemCode).where(RedeemCode.code == redeem_code.code)
    )
    if existing.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="兑换码已存在")
    
    # 验证兑换码类型
    if not redeem_code.credits and not redeem_code.package_id:
        raise HTTPException(status_code=400, detail="必须指定积分数量或套餐ID")
    
    # 创建兑换码
    db_redeem_code = RedeemCode(
        **redeem_code.model_dump(),
        created_by=current_user.id
    )
    
    db.add(db_redeem_code)
    await db.commit()
    await db.refresh(db_redeem_code)
    
    code_dict = db_redeem_code.__dict__.copy()
    code_dict['is_expired'] = False
    code_dict['usage_rate'] = 0.0
    
    return RedeemCodeResponse.model_validate(code_dict)

@router.put("/redeem-codes/{code_id}", response_model=RedeemCodeResponse)
async def update_redeem_code(
    code_id: int,
    code_update: RedeemCodeUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新兑换码"""
    
    result = await db.execute(select(RedeemCode).where(RedeemCode.id == code_id))
    redeem_code = result.scalar_one_or_none()
    
    if not redeem_code:
        raise HTTPException(status_code=404, detail="兑换码不存在")
    
    # 更新字段
    update_data = code_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(redeem_code, field, value)
    
    redeem_code.updated_at = datetime.utcnow()
    
    await db.commit()
    await db.refresh(redeem_code)
    
    code_dict = redeem_code.__dict__.copy()
    code_dict['is_expired'] = is_datetime_expired(redeem_code.expires_at)
    code_dict['usage_rate'] = (
        redeem_code.used_count / redeem_code.max_uses * 100 
        if redeem_code.max_uses > 0 else 0
    )
    
    return RedeemCodeResponse.model_validate(code_dict)


@router.delete("/redeem-codes/{code_id}")
async def delete_redeem_code(
    code_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """删除兑换码"""

    result = await db.execute(select(RedeemCode).where(RedeemCode.id == code_id))
    redeem_code = result.scalar_one_or_none()

    if not redeem_code:
        raise HTTPException(status_code=404, detail="兑换码不存在")

    # 检查兑换码是否已被使用
    if redeem_code.used_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除已被使用的兑换码，当前使用次数: {redeem_code.used_count}"
        )

    # 检查是否有关联的兑换记录
    redemption_result = await db.execute(
        select(RedeemCodeRedemption).where(RedeemCodeRedemption.redeem_code_id == code_id)
    )
    redemptions = redemption_result.scalars().all()

    if redemptions:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除有兑换记录的兑换码，关联记录数: {len(redemptions)}"
        )

    # 删除兑换码
    await db.delete(redeem_code)
    await db.commit()

    return {"message": f"兑换码 {redeem_code.code} 删除成功"}


# 兑换记录管理
@router.get("/redemptions", response_model=PaginatedResponse)
async def get_redemptions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    redeem_code_id: Optional[int] = Query(None, description="兑换码ID筛选"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取兑换记录列表"""
    
    query = select(
        RedeemCodeRedemption,
        RedeemCode.code,
        User.username,
        User.email,
        User.wechat_nickname,
        User.nickname
    ).join(RedeemCode, RedeemCodeRedemption.redeem_code_id == RedeemCode.id)\
     .join(User, RedeemCodeRedemption.user_id == User.id)
    
    # 添加筛选条件
    conditions = []
    if user_id:
        conditions.append(RedeemCodeRedemption.user_id == user_id)
    if redeem_code_id:
        conditions.append(RedeemCodeRedemption.redeem_code_id == redeem_code_id)
    if start_date:
        conditions.append(RedeemCodeRedemption.redeemed_at >= start_date)
    if end_date:
        conditions.append(RedeemCodeRedemption.redeemed_at <= end_date)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # 获取总数
    count_query = select(func.count(RedeemCodeRedemption.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    query = query.order_by(desc(RedeemCodeRedemption.redeemed_at))
    query = query.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(query)
    rows = result.all()
    
    # 构建响应数据
    items = []
    for row in rows:
        redemption, redeem_code, username, email, wechat_nickname, nickname = row
        redemption_dict = {
            **redemption.__dict__,
            'redeem_code': redeem_code,
            'user_username': username,
            'user_email': email,
            'user_wechat_nickname': wechat_nickname,
            'user_nickname': nickname
        }
        items.append(RedeemCodeRedemptionResponse.model_validate(redemption_dict))
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=math.ceil(total / page_size)
    )

# 用户积分账户管理
@router.get("/credit-accounts", response_model=PaginatedResponse)
async def get_credit_accounts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(None, description="账户状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词(用户名/邮箱)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取用户积分账户列表"""
    
    query = select(
        UserCreditAccount,
        User.username,
        User.email
    ).join(User, UserCreditAccount.user_id == User.id)
    
    # 添加筛选条件
    conditions = []
    if is_active is not None:
        conditions.append(UserCreditAccount.is_active == is_active)
    if search:
        conditions.append(
            or_(
                User.username.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%")
            )
        )
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # 获取总数
    count_query = select(func.count(UserCreditAccount.id))
    if conditions:
        count_query = count_query.join(User, UserCreditAccount.user_id == User.id).where(and_(*conditions))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    query = query.order_by(desc(UserCreditAccount.created_at))
    query = query.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(query)
    rows = result.all()
    
    # 构建响应数据
    items = []
    for row in rows:
        account, username, email = row
        account_dict = {
            **account.__dict__,
            'user_username': username,
            'user_email': email
        }
        items.append(UserCreditAccountResponse.model_validate(account_dict))
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=math.ceil(total / page_size)
    )

# 积分交易记录
@router.get("/credit-transactions", response_model=PaginatedResponse)
async def get_credit_transactions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    transaction_type: Optional[str] = Query(None, description="交易类型筛选"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取积分交易记录列表"""
    
    query = select(
        CreditTransaction,
        User.username,
        PaymentOrder.order_no
    ).join(User, CreditTransaction.user_id == User.id)\
     .outerjoin(PaymentOrder, CreditTransaction.order_id == PaymentOrder.id)
    
    # 添加筛选条件
    conditions = []
    if user_id:
        conditions.append(CreditTransaction.user_id == user_id)
    if transaction_type:
        conditions.append(CreditTransaction.transaction_type == transaction_type)
    if start_date:
        conditions.append(CreditTransaction.created_at >= start_date)
    if end_date:
        conditions.append(CreditTransaction.created_at <= end_date)
    
    if conditions:
        query = query.where(and_(*conditions))
    
    # 获取总数
    count_query = select(func.count(CreditTransaction.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))
    
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # 分页查询
    query = query.order_by(desc(CreditTransaction.created_at))
    query = query.offset((page - 1) * page_size).limit(page_size)
    
    result = await db.execute(query)
    rows = result.all()
    
    # 构建响应数据
    items = []
    for row in rows:
        transaction, username, order_no = row
        transaction_dict = {
            **transaction.__dict__,
            'user_username': username,
            'order_no': order_no
        }
        items.append(CreditTransactionResponse.model_validate(transaction_dict))
    
    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=math.ceil(total / page_size)
    )

# 订阅统计
@router.get("/stats", response_model=SubscriptionStats)
async def get_subscription_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取订阅统计数据"""
    
    # 使用北京时间（UTC+8）来计算今天和昨天
    beijing_tz = timezone(timedelta(hours=8))
    now_beijing = datetime.now(beijing_tz)
    today_start_beijing = now_beijing.replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday_start_beijing = today_start_beijing - timedelta(days=1)
    
    # 转换为UTC时间用于数据库查询
    today_start = today_start_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    yesterday_start = yesterday_start_beijing.astimezone(timezone.utc).replace(tzinfo=None)
    week_ago = today_start - timedelta(days=7)
    
    # 总订单数
    total_orders_result = await db.execute(select(func.count(PaymentOrder.id)))
    total_orders = total_orders_result.scalar() or 0
    
    # 待支付订单数
    pending_orders_result = await db.execute(
        select(func.count(PaymentOrder.id)).where(PaymentOrder.status == "pending")
    )
    pending_orders = pending_orders_result.scalar() or 0
    
    # 已支付订单数
    paid_orders_result = await db.execute(
        select(func.count(PaymentOrder.id)).where(PaymentOrder.status == "paid")
    )
    paid_orders = paid_orders_result.scalar() or 0
    
    # 总收入
    total_revenue_result = await db.execute(
        select(func.sum(PaymentOrder.amount)).where(PaymentOrder.status == "paid")
    )
    total_revenue = total_revenue_result.scalar() or 0
    
    # 总销售积分
    total_credits_result = await db.execute(
        select(func.sum(PaymentOrder.credits)).where(PaymentOrder.status == "paid")
    )
    total_credits_sold = total_credits_result.scalar() or 0
    
    # 活跃兑换码数
    active_codes_result = await db.execute(
        select(func.count(RedeemCode.id)).where(
            and_(
                RedeemCode.is_active == True,
                or_(
                    RedeemCode.expires_at.is_(None),
                    RedeemCode.expires_at > now_beijing.astimezone(timezone.utc).replace(tzinfo=None)
                )
            )
        )
    )
    active_redeem_codes = active_codes_result.scalar() or 0
    
    # 总兑换次数
    total_redemptions_result = await db.execute(select(func.count(RedeemCodeRedemption.id)))
    total_redemptions = total_redemptions_result.scalar() or 0
    
    # 计算增长率 - 今日vs昨日
    # 今日订单数
    today_orders_result = await db.execute(
        select(func.count(PaymentOrder.id)).where(PaymentOrder.created_at >= today_start)
    )
    today_orders = today_orders_result.scalar() or 0
    
    # 昨日订单数
    yesterday_orders_result = await db.execute(
        select(func.count(PaymentOrder.id)).where(
            and_(
                PaymentOrder.created_at >= yesterday_start,
                PaymentOrder.created_at < today_start
            )
        )
    )
    yesterday_orders = yesterday_orders_result.scalar() or 0
    
    # 今日收入
    today_revenue_result = await db.execute(
        select(func.sum(PaymentOrder.amount)).where(
            and_(
                PaymentOrder.created_at >= today_start,
                PaymentOrder.status == "paid"
            )
        )
    )
    today_revenue = today_revenue_result.scalar() or 0
    
    # 昨日收入
    yesterday_revenue_result = await db.execute(
        select(func.sum(PaymentOrder.amount)).where(
            and_(
                PaymentOrder.created_at >= yesterday_start,
                PaymentOrder.created_at < today_start,
                PaymentOrder.status == "paid"
            )
        )
    )
    yesterday_revenue = yesterday_revenue_result.scalar() or 0
    
    # 今日积分销售
    today_credits_result = await db.execute(
        select(func.sum(PaymentOrder.credits)).where(
            and_(
                PaymentOrder.created_at >= today_start,
                PaymentOrder.status == "paid"
            )
        )
    )
    today_credits = today_credits_result.scalar() or 0
    
    # 昨日积分销售
    yesterday_credits_result = await db.execute(
        select(func.sum(PaymentOrder.credits)).where(
            and_(
                PaymentOrder.created_at >= yesterday_start,
                PaymentOrder.created_at < today_start,
                PaymentOrder.status == "paid"
            )
        )
    )
    yesterday_credits = yesterday_credits_result.scalar() or 0
    
    # 今日兑换次数
    today_redemptions_result = await db.execute(
        select(func.count(RedeemCodeRedemption.id)).where(
            RedeemCodeRedemption.redeemed_at >= today_start
        )
    )
    today_redemptions = today_redemptions_result.scalar() or 0
    
    # 昨日兑换次数
    yesterday_redemptions_result = await db.execute(
        select(func.count(RedeemCodeRedemption.id)).where(
            and_(
                RedeemCodeRedemption.redeemed_at >= yesterday_start,
                RedeemCodeRedemption.redeemed_at < today_start
            )
        )
    )
    yesterday_redemptions = yesterday_redemptions_result.scalar() or 0
    
    # 计算增长率
    def calculate_growth_rate(today_value, yesterday_value):
        if yesterday_value > 0:
            return round(((today_value - yesterday_value) / yesterday_value) * 100, 1)
        else:
            return 100.0 if today_value > 0 else 0.0
    
    orders_growth_rate = calculate_growth_rate(today_orders, yesterday_orders)
    revenue_growth_rate = calculate_growth_rate(float(today_revenue), float(yesterday_revenue))
    credits_growth_rate = calculate_growth_rate(today_credits, yesterday_credits)
    redemptions_growth_rate = calculate_growth_rate(today_redemptions, yesterday_redemptions)
    
    return SubscriptionStats(
        total_orders=total_orders,
        pending_orders=pending_orders,
        paid_orders=paid_orders,
        total_revenue=total_revenue,
        total_credits_sold=total_credits_sold,
        active_redeem_codes=active_redeem_codes,
        total_redemptions=total_redemptions,
        orders_growth_rate=orders_growth_rate,
        revenue_growth_rate=revenue_growth_rate,
        credits_growth_rate=credits_growth_rate,
        redemptions_growth_rate=redemptions_growth_rate
    )


# 套餐配置管理
@router.get("/package-configs", response_model=PaginatedResponse)
async def get_package_configs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选"),
    is_trial: Optional[bool] = Query(None, description="是否体验套餐筛选"),
    billing_cycle: Optional[str] = Query(None, description="计费周期筛选"),
    search: Optional[str] = Query(None, description="搜索关键词(套餐名称/描述)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """获取套餐配置列表"""

    query = select(PackageConfig)

    # 添加筛选条件
    conditions = []
    if is_active is not None:
        conditions.append(PackageConfig.is_active == is_active)
    if is_trial is not None:
        conditions.append(PackageConfig.is_trial == is_trial)
    if billing_cycle:
        conditions.append(PackageConfig.billing_cycle == billing_cycle)
    if search:
        conditions.append(
            or_(
                PackageConfig.name.ilike(f"%{search}%"),
                PackageConfig.description.ilike(f"%{search}%")
            )
        )

    if conditions:
        query = query.where(and_(*conditions))

    # 获取总数
    count_query = select(func.count(PackageConfig.id))
    if conditions:
        count_query = count_query.where(and_(*conditions))

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # 分页查询
    query = query.order_by(PackageConfig.sort_order, desc(PackageConfig.created_at))
    query = query.offset((page - 1) * page_size).limit(page_size)

    result = await db.execute(query)
    package_configs = result.scalars().all()

    # 构建响应数据
    items = [PackageConfigResponse.model_validate(config.__dict__) for config in package_configs]

    return PaginatedResponse(
        items=items,
        total=total,
        page=page,
        page_size=page_size,
        pages=math.ceil(total / page_size)
    )


@router.post("/package-configs", response_model=PackageConfigResponse)
async def create_package_config(
    package_config: PackageConfigCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """创建套餐配置"""

    # 检查套餐ID是否已存在
    existing = await db.execute(
        select(PackageConfig).where(PackageConfig.package_id == package_config.package_id)
    )
    if existing.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="套餐ID已存在")

    # 创建套餐配置
    db_package_config = PackageConfig(**package_config.model_dump())

    db.add(db_package_config)
    await db.commit()
    await db.refresh(db_package_config)

    return PackageConfigResponse.model_validate(db_package_config.__dict__)


@router.put("/package-configs/{config_id}", response_model=PackageConfigResponse)
async def update_package_config(
    config_id: int,
    config_update: PackageConfigUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """更新套餐配置"""

    result = await db.execute(select(PackageConfig).where(PackageConfig.id == config_id))
    package_config = result.scalar_one_or_none()

    if not package_config:
        raise HTTPException(status_code=404, detail="套餐配置不存在")

    # 更新字段
    update_data = config_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(package_config, field, value)

    package_config.updated_at = datetime.now(timezone.utc)

    await db.commit()
    await db.refresh(package_config)

    return PackageConfigResponse.model_validate(package_config.__dict__)


@router.post("/custom-package-redeem-code", response_model=CustomPackageRedeemCodeResponse)
async def create_custom_package_redeem_code(
    request: CustomPackageRedeemCodeCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """创建自定义套餐和对应的兑换码

    这个端点实现了两个核心步骤：
    1. 创建自定义套餐配置
    2. 创建绑定到该套餐的兑换码

    使用事务确保两个步骤的原子性。
    """

    try:
        # 第一步：验证套餐ID是否已存在
        existing_package = await db.execute(
            select(PackageConfig).where(
                PackageConfig.package_id == request.package_config.package_id
            )
        )
        if existing_package.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="套餐ID已存在")

        # 验证兑换码是否已存在
        existing_code = await db.execute(
            select(RedeemCode).where(RedeemCode.code == request.redeem_code)
        )
        if existing_code.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="兑换码已存在")

        # 验证套餐数据完整性
        package_data = request.package_config.model_dump()

        # 清理空值：将前端的 null 转换为 Python 的 None
        if package_data.get("bulk_discount") is None:
            package_data["bulk_discount"] = None
        if package_data.get("monthly_price") is None:
            package_data["monthly_price"] = None
        if package_data.get("validity_days") is None:
            package_data["validity_days"] = None
        if package_data.get("contact_phone") == "":
            package_data["contact_phone"] = None

        # 验证计费周期
        valid_billing_cycles = ["trial", "yearly", "monthly"]
        if package_data["billing_cycle"] not in valid_billing_cycles:
            raise HTTPException(
                status_code=400,
                detail=f"无效的计费周期，必须是: {', '.join(valid_billing_cycles)}"
            )

        # 验证用户数量范围
        if package_data["min_users"] > package_data["max_users"]:
            raise HTTPException(status_code=400, detail="最少用户数不能大于最多用户数")

        # 验证体验套餐的有效期
        if package_data["is_trial"] and not package_data.get("validity_days"):
            raise HTTPException(status_code=400, detail="体验套餐必须设置有效期天数")

        # 验证联系销售配置
        if package_data["contact_required"] and not package_data.get("contact_phone"):
            raise HTTPException(status_code=400, detail="需要联系销售的套餐必须提供联系电话")

        # 第二步：创建套餐配置
        db_package_config = PackageConfig(**package_data)
        db.add(db_package_config)
        await db.flush()  # 刷新以获取ID，但不提交事务

        # 第三步：创建兑换码
        redeem_code_data = {
            "code": request.redeem_code,
            "package_id": request.package_config.package_id,  # 绑定到套餐
            "description": request.redeem_description or f"自定义套餐兑换码: {request.package_config.name}",
            "max_uses": request.max_uses,
            "expires_at": request.expires_at,
            "is_active": True,
            "created_by": current_user.id
        }

        db_redeem_code = RedeemCode(**redeem_code_data)
        db.add(db_redeem_code)
        await db.flush()  # 刷新以获取ID

        # 在提交前构建响应数据，避免提交后出错
        try:
            # 刷新对象以获取最新数据（包括自动生成的字段）
            await db.refresh(db_package_config)
            await db.refresh(db_redeem_code)

            # 构建套餐响应数据
            package_dict = {
                "id": db_package_config.id,
                "package_id": db_package_config.package_id,
                "name": db_package_config.name,
                "description": db_package_config.description,
                "amount": float(db_package_config.amount),
                "credits": db_package_config.credits,
                "bonus": db_package_config.bonus,
                "billing_cycle": db_package_config.billing_cycle,
                "monthly_price": float(db_package_config.monthly_price) if db_package_config.monthly_price else None,
                "min_users": db_package_config.min_users,
                "max_users": db_package_config.max_users,
                "validity_days": db_package_config.validity_days,
                "is_trial": db_package_config.is_trial,
                "contact_required": db_package_config.contact_required,
                "contact_phone": db_package_config.contact_phone,
                "features": db_package_config.features,
                "bulk_discount": db_package_config.bulk_discount,
                "is_active": db_package_config.is_active,
                "sort_order": db_package_config.sort_order,
                "created_at": db_package_config.created_at,
                "updated_at": db_package_config.updated_at
            }
            package_response = PackageConfigResponse.model_validate(package_dict)

            # 构建兑换码响应数据
            code_dict = {
                "id": db_redeem_code.id,
                "code": db_redeem_code.code,
                "credits": db_redeem_code.credits,
                "package_id": db_redeem_code.package_id,
                "description": db_redeem_code.description,
                "max_uses": db_redeem_code.max_uses,
                "used_count": db_redeem_code.used_count,
                "is_active": db_redeem_code.is_active,
                "expires_at": db_redeem_code.expires_at,
                "created_by": db_redeem_code.created_by,
                "created_at": db_redeem_code.created_at,
                "updated_at": db_redeem_code.updated_at,
                "is_expired": is_datetime_expired(db_redeem_code.expires_at),
                "usage_rate": 0.0
            }
            redeem_code_response = RedeemCodeResponse.model_validate(code_dict)

            # 构建最终响应
            response = CustomPackageRedeemCodeResponse(
                package_config=package_response,
                redeem_code=redeem_code_response
            )

            # 只有在响应构建成功后才提交事务
            await db.commit()

            return response

        except Exception as e:
            # 如果响应构建失败，回滚事务
            await db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"响应数据构建失败: {str(e)}"
            )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 回滚事务并抛出服务器错误
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"创建自定义套餐和兑换码失败: {str(e)}"
        )