from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime
from app.core.dependencies import get_db, security
from app.core.security import (
    verify_password, 
    create_access_token, 
    create_refresh_token,
    verify_refresh_token
)
from app.models.user import User
from app.schemas.user import UserLogin, Token, UserResponse
from app.core.config import settings

router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/login", response_model=Token, summary="管理员登录")
async def login(
    user_credentials: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """
    管理员登录接口
    
    - **username**: 用户名
    - **password**: 密码
    
    只允许管理员角色用户登录BMS系统
    """
    
    # 查询用户
    stmt = select(User).where(User.username == user_credentials.username)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    # 验证用户存在性
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 验证密码
    if not user.password_hash or not verify_password(user_credentials.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 验证用户状态
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="账户已被禁用"
        )
    
    # 验证管理员权限
    if user.role not in ["admin", "super_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只允许管理员登录"
        )
    
    # 更新最后登录时间
    stmt = update(User).where(User.id == user.id).values(last_login=datetime.utcnow())
    await db.execute(stmt)
    await db.commit()
    
    # 创建JWT令牌
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60
    }

@router.post("/refresh", response_model=Token, summary="刷新访问令牌")
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    刷新访问令牌
    
    使用有效的刷新令牌获取新的访问令牌
    """
    
    # 验证刷新令牌
    token_data = verify_refresh_token(credentials.credentials)
    
    # 验证用户仍然存在且活跃
    stmt = select(User).where(User.id == token_data["user_id"])
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 创建新的访问令牌
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.access_token_expire_minutes * 60
    }

@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前认证用户的详细信息
    """
    from app.core.dependencies import get_current_user
    from app.schemas.user import UserInDB
    
    # 这里需要手动调用依赖，因为在路由内部
    user = await get_current_user(credentials, db)
    
    return UserResponse.model_validate(user)